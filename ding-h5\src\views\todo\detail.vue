<template>
  <div class="todo-detail">
    <!-- 顶部标题栏 -->
    <!-- <div class="header">
      <van-nav-bar
        title="待办详情"
        left-arrow
        @click-left="router.back()"
      />
    </div> -->

    <!-- 内容区域 -->
    <div class="content">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="title-row">
          <span class="title">{{ todoData.eventTitle }}</span>
          <van-tag :type="getStatusType(todoData.status)">{{ getStatusText(todoData.status) }}</van-tag>
        </div>

        <div class="info-row">
          <span class="label">涉事主体:</span>
          <span class="value">{{ todoData.eventTarget }}</span>
        </div>

        <div class="info-row">
          <span class="label">举报投诉日期：</span>
          <span class="value">{{ todoData.rollDate }}</span>
        </div>

        <div class="info-row">
          <span class="label">工单类型：</span>
          <span class="value">{{ todoData.rollType }}</span>
        </div>

        <div class="info-row">
          <span class="label">问题分类：</span>
          <span class="value">{{ todoData.eventType }}</span>
        </div>

        <div class="info-row">
          <span class="label">处理人：</span>
          <span class="value">{{ todoData.userName }}</span>
        </div>
      </div>

      <!-- 详细描述卡片 -->
      <div class="desc-card">
        <div class="card-title">详细描述</div>
        <div class="desc-content">{{ todoData.eventContent }}</div>
      </div>

      <div style="border-radius: 8px;overflow: hidden;">
        <!-- 零售户选择 -->
        <LicenseSearchSelect v-model="licenseIdList" @select="handleLicenseSelect" :selected-items="licenseItems" label="零售户" placeholder="请选择"/>

        <!-- 品规选择已屏蔽 -->
        <!-- <ItemSearchSelect v-model="itemIdList" :selected-items="itemItems" @select="handleItemSelect" label="品规" placeholder="请选择"  /> -->

        <!-- 处理结果输入 - 使用与零售户选择相同的样式，标签和输入框在同一行 -->
        <div class="search-select-wrapper result-wrapper">
          <div class="result-row">
            <div class="search-select-label">处理结果</div>
            <van-field
              v-model="handleResult"
              type="textarea"
              placeholder="请输入处理结果"
              rows="3"
              class="result-field"
            />
          </div>
        </div>
      </div>

    </div>

    <!-- 底部操作按钮 -->
    <div class="footer">
      <van-button type="primary" block @click="handleProcess">提交</van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showSuccessToast, showFailToast } from 'vant'
import { http } from '@/utils/http'
// 品规选择已屏蔽，但保留导入以便将来可能恢复
// import ItemSearchSelect from '@/components/ItemSearchSelect.vue'
import LicenseSearchSelect from '@/components/LicenseSearchSelect.vue'


const route = useRoute()
const router = useRouter()

// 待办数据
const todoData = ref({
  id: '',
  title: '',
  description: '',
  status: 'pending',
  createTime: '',
  deadline: '',
  handler: '',
  attachments: []
})

// 获取状态类型
const getStatusType = () => {
  // 修改判断条件：只需要选择零售户和填写处理结果
  return licenseIdList.value.length > 0 && handleResult.value.trim() !== '' ? 'success' : 'warning'
}

// 获取状态文本
const getStatusText = () => {
  // 修改判断条件：只需要选择零售户和填写处理结果
  return licenseIdList.value.length > 0 && handleResult.value.trim() !== '' ? '已完成' : '待处理'
}




// 处理待办
const handleProcess = async () => {
  try {
    // 验证必填项
    if (licenseIdList.value.length === 0) {
      showFailToast('请选择零售户')
      return
    }

    if (handleResult.value.trim() === '') {
      showFailToast('请输入处理结果')
      return
    }

    // 构建提交数据
    let data = {
      objId: todoData.value.id,
      handleResult: handleResult.value, // 将处理结果添加到提交数据中
      reportIllegalLabelList: [] // 标签列表
    }

    // 添加零售户标签
    if(licenseIdList.value.length > 0) {
      data.reportIllegalLabelList = licenseIdList.value.map(id => ({
        labelType: '零售户',
        labelId: id,
        objId: todoData.value.id,
        objType: '工单'
      }))
    }

    // 一次性提交所有数据（标签和处理结果）
    await http.post('/api/dingapp/reportIllegalLabel/submit', data)

    showSuccessToast('提交成功')
    router.back()
  } catch (error) {
    showFailToast('提交失败')
    console.error('提交失败:', error)
  }
}
const licenseIdList = ref([])
const itemIdList = ref([]) // 保留但不再使用
const licenseItems = ref([])
const itemItems = ref([]) // 保留但不再使用
const handleResult = ref('') // 新增处理结果字段
// 获取待办详情
const getTodoDetail = async () => {
  try {
    const res = await http.get(`/api/dingapp/reportcomplaint/detail`,{
      params: {
        id: route.query.id
      }
    })
    if (res?.data) {
      todoData.value = res.data

      // 设置处理结果（如果有）
      if (res.data.handleResult) {
        handleResult.value = res.data.handleResult
      }

      // 处理标签数据
      const { licenseList, itemList } = res.data.reportIllegalLabelList.reduce(
        (acc, obj) => {
          if (obj.labelType === '零售户') {
            acc.licenseList.push(obj);
          } else if(obj.labelType === '品规') {
            acc.itemList.push(obj);
          }
          return acc;
        },
        { licenseList: [], itemList: [] }
      );

      // 提取零售户信息
      licenseItems.value = licenseList.map(item => ({
        value: item.labelId,
        text: item.yhytLicense?.companyName || '未知零售户',
        selected: true
      }));
      licenseIdList.value = licenseItems.value.map(item => item.value);

      // 品规信息已不再使用，但保留代码以便将来可能恢复
      itemItems.value = itemList.map(item => ({
        value: item.labelId,
        text: item.productInfo?.productName || '未知品规',
        selected: true
      }));
      itemIdList.value = itemItems.value.map(item => item.value);
    }
  } catch (error) {
    showFailToast('获取详情失败')
    console.error('获取详情失败:', error)
  }
}


// 处理零售户选择
const handleLicenseSelect = (selectedItems) => {
  // 更新licenseItems以保持UI显示同步
  licenseItems.value = selectedItems;
  // 提取ID列表用于提交
  licenseIdList.value = selectedItems.map(item => item.value);
}

onMounted(() => {

  if (route.query.id) {
    getTodoDetail()
  }
})
</script>

<style lang="scss" scoped>
.todo-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 16px;
}

.info-card,
.desc-card,
.attachment-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.title-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .title {
    font-size: 18px;
    font-weight: bold;
    color: #323233;
    flex: 1;
    margin-right: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  :deep(.van-tag) {
    flex-shrink: 0;
    margin-top: 4px;
  }
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
  align-items: flex-start;
  line-height: 1.4;

  .label {
    color: #969799;
    width: 100px;
    flex-shrink: 0;
    padding: 4px 0;
  }

  .value {
    color: #323233;
    flex: 1;
    padding: 4px 0;
  }
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 12px;
}

.desc-content {
  font-size: 14px;
  color: #323233;
  line-height: 1.5;
}

.attachment-list {
  .attachment-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;

    .file-icon {
      font-size: 20px;
      color: #1989fa;
      margin-right: 8px;
    }

    .file-name {
      font-size: 14px;
      color: #323233;
    }
  }
}

// 处理结果输入框样式
.search-select-wrapper {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #f2f3f5;

  .search-select-label {
    font-size: 14px;
    color: #646566;
    margin-bottom: 8px;
  }

  // 修复van-field的边距问题
  :deep(.van-cell) {
    padding: 0;
    margin: 0;
    width: 100%;
  }

  // 处理结果输入框样式
  .result-field {
    :deep(.van-field__control) {
      min-height: 60px;
      background-color: #f7f8fa;
      border-radius: 4px;
      padding: 8px;
    }

    // 确保文本可以换行显示
    :deep(.van-field__body) {
      white-space: normal !important;
      word-break: break-all;
    }
  }
}

// 处理结果行样式 - 确保标签和输入框在同一行
.result-wrapper {
  .result-row {
    display: flex;
    align-items: flex-start;

    .search-select-label {
      width: 80px;
      flex-shrink: 0;
      margin-top: 12px; // 垂直居中对齐
      margin-bottom: 0;
      margin-right: 12px;
    }

    .result-field {
      flex: 1;
    }
  }
}

.footer {
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}
</style>