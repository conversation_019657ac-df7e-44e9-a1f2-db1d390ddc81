<template>
    <div v-if="visible" class="ant-toast-container" :class="{ 'with-mask': showMask }">
        <div v-if="showMask" class="ant-toast-mask" @click="handleMaskClick"></div>
        <div class="ant-toast">
            <div class="ant-toast-content">{{ content }}</div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, onMounted, watch } from 'vue';

const props = defineProps({
    content: {
        type: String,
        default: ''
    },
    visible: {
        type: Boolean,
        default: false
    },
    duration: {
        type: Number,
        default: 2000
    },
    showMask: {
        type: Boolean,
        default: false
    },
    maskCloseable: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['close']);

// 处理遮罩点击事件
const handleMaskClick = () => {
    if (props.maskCloseable) {
        emit('close');
    }
};

// 监听visible变化，自动关闭
watch(() => props.visible, (newVal) => {
    if (newVal && props.duration > 0) {
        setTimeout(() => {
            emit('close');
        }, props.duration);
    }
});

// 组件挂载时，如果visible为true，设置自动关闭
onMounted(() => {
    if (props.visible && props.duration > 0) {
        setTimeout(() => {
            emit('close');
        }, props.duration);
    }
});
</script>

<style scoped>
.ant-toast-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.ant-toast-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
}

.ant-toast {
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 10px 16px;
    border-radius: 4px;
    max-width: 80%;
    z-index: 10000;
    text-align: center;
}

.ant-toast-content {
    font-size: 14px;
    line-height: 1.5;
}

.with-mask .ant-toast {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>