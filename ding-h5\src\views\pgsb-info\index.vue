<template>
  <div class="pgsb-info">
      <van-cell-group class="section" title="烟柜">
          <!-- <div v-if="isView" class="pgsb-img-box">
              <img v-for="(item, index) in yanguiFileList" :key="index" :src="item.url"
                  @click="onYanguiImgClick(index)" />
          </div> -->
          <van-uploader :deletable="!isView" :readonly="isView" :upload-text="isView?'仅允许查看':''" :preview-full-image="false" v-model="yanguiFileList" :after-read="onYanguiUpload" @delete="handleYanguiRemove"
              @click-preview="onYanguiPreview" @error="onChooseImageError" />
      </van-cell-group>
      <van-cell-group class="section" title="烟架">
          <!-- <div v-if="isView" class="pgsb-img-box">
              <img v-for="(item, index) in yanjiaFileList" :key="index" :src="item.url"
                  @click="onYanjiaImgClick(index)" />
          </div> -->
          <van-uploader :deletable="!isView" :readonly="isView" :upload-text="isView?'仅允许查看':''" :preview-full-image="false" v-model="yanjiaFileList" :after-read="onYanjiaUpload" @delete="handleYanjiaRemove"
              @click-preview="onYanjiaPreview" @error="onChooseImageError" />
      </van-cell-group>

      <van-cell-group class="section">
          <div class="advice-container">
              <div class="advice-label">专家建议：</div>
              <div class="advice-content">{{ adviceText }}</div>
          </div>
          <!-- <div v-if="startHour && endHour" class="advice-container">
              <div class="advice-label">检查时间段：</div>
              <div class="advice-content">{{  }} </div>
          </div> -->
      </van-cell-group>

      <van-cell-group class="section" title="对碰结果">
          <div class="table-container">
              <div class="table-header">
                  <div class="col-item" style="width: 60px">序号</div>
                  <div class="col-item" style="width: 130px">品规码</div>
                  <div class="col-item" style="width: 300px">品规</div>
                  <div class="col-item" style="width: 180px">嫌疑类别</div>
              </div>
              <template v-if="collisionResult.length">
              <div class="table-body">
                  <div v-for="(item, index) in collisionResult" :key="index" class="table-row">
                      <div class="col-item" style="width: 60px">{{ index + 1 }}</div>
                      <div class="col-item" style="width: 130px">{{ item.itemCode }}</div>
                      <div class="col-item" style="width: 300px">{{ item.itemName }}</div>
                      <div class="col-item" :class="getCollisionTypeClass(item.collisionType)" style="width: 180px">{{ item.collisionType }}</div>
                  </div>
              </div>
          </template>
          <van-empty v-else description="暂无数据"   image-size="100" />
          </div>
      </van-cell-group>

      <!-- <van-cell-group class="section" title="识别结果">
          <div class="table-container">
              <div class="table-header">
                  <div class="col-item" style="width: 60px">序号</div>
                  <div class="col-item" style="width: 130px">品规码</div>
                  <div class="col-item" style="width: 500px">品规</div>
              </div>
              <template v-if="identificationResult.length">
              <div class="table-body">
                  <div v-for="(item, index) in identificationResult" :key="index" class="table-row">
                      <div class="col-item" style="width: 60px">{{ index + 1 }}</div>
                      <div class="col-item" style="width: 130px">{{ item.itemCode }}</div>
                      <div class="col-item" style="width: 500px">{{ item.itemName }}</div>
                  </div>
              </div>
          </template>
          <van-empty v-else description="暂无数据" image-size="100" />
          </div>
      </van-cell-group> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute,useRouter } from 'vue-router'
import { http } from '@/utils/http'
import { showSuccessToast, showFailToast   } from 'vant'
import { useLicenseStore } from '@/stores/license'

const route = useRoute()
const router = useRouter()

const yanguiFileList = ref([])
const yanjiaFileList = ref([])
const isView = ref(false)
const licData = ref({})
const date = ref('')
const timeRange = ref('') // 存储时间段
const startTime = ref('') // 存储开始时间
const endTime = ref('') // 存储结束时间
const startHour = ref('') // 存储开始小时数字
const endHour = ref('') // 存储结束小时数字
const adviceText = ref('无;')

const collisionResult = ref([])
const identificationResult = ref([])

const getListData = async () => {
  await getImgData()
  await getItemIdentifyResults()
}

const getImgData = async () => {
  try {
      const res = await http.get('/api/dingapp/itemIdentify/listByExplorationId', {
          params: {
              explorationId: licData.value?.explorationId || '',
              identifyDate: date.value,
              startHour: startHour.value,
              endHour: endHour.value,
          }
      })

      if (res.data) {
          const filteredCabinet = res.data
              .filter(item => item.type === '烟柜')
              .map(item => ({
                  uid: item.id,
                  url: item.fileUrl,
                  resultFileUrl: item.resultFileUrl
              }))

          const filteredRack = res.data
              .filter(item => item.type === '烟架')
              .map(item => ({
                  uid: item.id,
                  url: item.fileUrl,
                  resultFileUrl: item.resultFileUrl
              }))

          yanguiFileList.value = filteredCabinet
          yanjiaFileList.value = filteredRack
      }
  } catch (error) {
      showFailToast('获取图片数据失败')
      console.error('获取图片数据失败:', error)
  }
}

const getItemIdentifyResults = async () => {
  try {
      const res = await http.get('/api/dingapp/itemIdentifyResults/list', {
          params: {
              explorationId: licData.value?.explorationId || '',
              identifyDate: date.value,
              startHour: startHour.value,
              endHour: endHour.value,
          }
      })

      if (res.data) {
          identificationResult.value = res.data

          // 对碰结果排序
          const errorData = [...res.data]
          errorData.sort((a, b) => {
              if (a.collisionType > b.collisionType) return -1
              if (a.collisionType < b.collisionType) return 1
              if (a.itemCode < b.itemCode) return -1
              if (a.itemCode > b.itemCode) return 1
              return 0
          })

          collisionResult.value = errorData

          const hasNonSmoke = errorData.some(item => item.collisionType.includes('非烟'))
          if (hasNonSmoke) {
              adviceText.value = '存在非烟嫌疑，请查处跟进；'
              const hasFakeSmoke = errorData.some(item => item.collisionType.includes('假烟'))
              if (hasFakeSmoke) {
                  adviceText.value = '存在非烟、假烟嫌疑，请查处跟进；'
              }
          }else{
              adviceText.value = '无；';
          }
      }
  } catch (error) {
      showFailToast('获取识别结果失败')
      console.error('获取识别结果失败:', error)
  }
}

const handleYanguiRemove = async (file) => {
  try {
      const res = await http.get('/api/dingapp/itemIdentify/remove', {
          params: {
              itemIdentifyId: file.uid
          }
      })

      if (res.data === true) {
          showSuccessToast('删除成功')
          await getListData()
      }
  } catch (error) {
      showFailToast('删除失败')
      console.error('删除失败:', error)
  }
}

const handleYanjiaRemove = async (file) => {
  try {
      const res = await http.get('/api/dingapp/itemIdentify/remove', {
          params: {
              itemIdentifyId: file.uid
          }
      })

      if (res.data === true) {
          showSuccessToast('删除成功')
          await getListData()
      }
  } catch (error) {
      showFailToast('删除失败')
      console.error('删除失败:', error)
  }
}

const getCollisionTypeClass = (type) => {
    switch (type) {
        case '重复':
            return 'collision-type-duplicate'
        case '非烟':
        case '非烟,假烟':
            return 'collision-type-error'
        case '正常':
            return 'collision-type-normal'
        default:
            return ''
    }
}

const onYanguiUpload = async (file) => {
  try {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('objName', "itemIdentify")
      formData.append('type', 'yangui')
      formData.append('explorationId', licData.value?.explorationId || '')
      formData.append('customerCode', licData.value?.yhytLicenseVO?.custCode || '')

      file.status = 'uploading';
      file.message = '上传中...';

      const res = await http.post('/api/dingapp/itemIdentify/upload', formData, {
          headers: {
              'Content-Type': 'multipart/form-data'
          }
      })

      if (res.data) {
        //   showSuccessToast('上传成功')
        file.status = 'done';
          await getListData()
      }
  } catch (error) {
    //   showFailToast('上传失败')
      file.status = 'failed';
      file.message = '上传失败';
      console.error('上传失败:', error)
  }
}

const onYanjiaUpload = async (file) => {
  try {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('objName', "itemIdentify")
      formData.append('type', 'yanjia')
      formData.append('explorationId', licData.value?.explorationId || '')
      formData.append('customerCode', licData.value?.yhytLicenseVO?.custCode || '')


      file.status = 'uploading';
      file.message = '上传中...';

      const res = await http.post('/api/dingapp/itemIdentify/upload', formData, {
          headers: {
              'Content-Type': 'multipart/form-data'
          }
      })

      if (res.data) {
        //   showSuccessToast('上传成功')
        file.status = 'done';
          await getListData()
      }
  } catch (error) {
    //   showFailToast('上传失败')
      file.status = 'failed';
      file.message = '上传失败';
      console.error('上传失败:', error)
  }
}

const onYanguiPreview = (file) => {
  if (file && file.resultFileUrl) {
    const url = `/pgsb-picture-info?url=${encodeURIComponent(file.resultFileUrl)}&id=${file.uid}`
    router.push(url)
  }
}

const onYanjiaPreview = (file) => {
  if (file && file.resultFileUrl) {
    const url = `/pgsb-picture-info?url=${encodeURIComponent(file.resultFileUrl)}&id=${file.uid}`
    router.push(url)
  }
}


const onChooseImageError = (error) => {
//   showFailToast('选择图片失败')
  console.error('选择图片失败:', error)
}

onMounted(async () => {
  try {
      const type = route.query.type
      const explorationIdFromQuery = route.query.explorationId
      const queryDate = route.query.date
      console.log(queryDate)

      // 提取时间段部分 (例如 "16:00-18:00")
      const extractedTimeRange = queryDate ? queryDate.match(/(\d{1,2}:\d{2}-\d{1,2}:\d{2})/)?.at(1) : ''
      timeRange.value = extractedTimeRange // 存储到响应式变量中

      // 拆分时间段为开始时间和结束时间
      if (extractedTimeRange) {
        const [start, end] = extractedTimeRange.split('-')
        startTime.value = start // 例如 "16:00"
        endTime.value = end // 例如 "18:00"

        // 提取小时数字
        startHour.value = start.split(':')[0] // 例如 "16"
        endHour.value = end.split(':')[0] // 例如 "18"

        console.log('开始时间:', startTime.value, '结束时间:', endTime.value)
        console.log('开始小时:', startHour.value, '结束小时:', endHour.value)
      }
      const licenseStore = useLicenseStore()
      if (type) {
        // 设置日期
        date.value = queryDate?.split(' ')[0]
        // 设置查看模式
        isView.value = type === 'view' || (date.value != new Date().toISOString().split('T')[0])
        // console.log(isView.value)
        // 获取许可证数据
        licData.value = licenseStore.currentLicense || {}
        //   console.log(licData.value)
        // 设置explorationId，优先使用URL中的，如果没有则使用store中的
        licData.value.explorationId = explorationIdFromQuery || licenseStore.getExplorationId()
        // 加载数据
        await getListData()
      }
  } catch (error) {
      showFailToast('初始化失败')
      console.error('初始化失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.pgsb-info {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 12px 0 50px;

  .section {
      margin-bottom: 12px;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
  }

  .pgsb-img-box {
      padding: 12px;

      img {
          width: 1.6rem;
          height: 1.6rem;
          margin: 0.04rem 0.08rem;
          border-radius: 0.08rem;
          object-fit: cover;
      }
  }

  .advice-container {
      padding: 16px;

      .advice-label {
          font-size: 14px;
          color: #333;
          margin-bottom: 8px;
      }

      .advice-content {
          font-size: 14px;
          color: #17a0bb;
          line-height: 1.5;
      }
  }

  :deep(.van-cell-group__title) {
      padding: 16px 16px 8px;
      font-size: 15px;
      font-weight: 500;
      color: #323233;
  }

  :deep(.van-uploader) {
      padding: 12px 16px;
  }

  .table-container {
      width: 100%;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;

      .table-header {
          display: flex;
          background-color: #f7f8fa;
          border-bottom: 1px solid #ebedf0;

          .col-item {
              padding: 12px 16px;
              font-size: 14px;
              font-weight: 500;
              color: #323233;
              white-space: nowrap;
          }
      }

      .table-body {
          .table-row {
              display: flex;
              border-bottom: 1px solid #ebedf0;

              &:last-child {
                  border-bottom: none;
              }

              .col-item {
                  padding: 12px 16px;
                  font-size: 14px;
                  color: #646566;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;

                    &.collision-type-duplicate {
                        color: #ff9800;
                    }

                    &.collision-type-error {
                        color: #f44336;
                    }

                    &.collision-type-normal {
                        color: #4caf50;
                    }
              }
          }

          .table-row:nth-child(even) {
              background-color: #fafafa;
          }
      }
  }
}
</style>