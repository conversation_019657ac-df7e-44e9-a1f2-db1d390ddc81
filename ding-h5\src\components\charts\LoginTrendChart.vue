<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      xAxis: [],
      series: []
    })
  }
});

const chartContainer = ref(null);
let chart = null;

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;
  
  // 创建图表实例
  chart = echarts.init(chartContainer.value);
  
  // 设置响应式
  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
  
  // 更新图表
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.chartData.xAxis,
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#eee'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      }
    },
    series: props.chartData.series.map(item => ({
      name: item.name,
      type: 'line',
      data: item.data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#1989fa'
      },
      lineStyle: {
        width: 3
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(25, 137, 250, 0.3)'
          },
          {
            offset: 1,
            color: 'rgba(25, 137, 250, 0.1)'
          }
        ])
      }
    }))
  };
  
  chart.setOption(option);
};

// 监听数据变化
watch(() => props.chartData, () => {
  updateChart();
}, { deep: true });

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
});

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', () => {
    chart && chart.resize();
  });
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
