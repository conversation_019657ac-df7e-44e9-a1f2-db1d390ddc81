<template>
    <div class="wfjl-main">
        <van-empty v-if="dataList.length === 0" description="数据为空" />
        <van-list v-else v-model:loading="loading" :finished="finished" finished-text="没有更多了"
            @load="scrollToLowerSearch">
            <div v-for="item in dataList" :key="item.regTime">
                <div class="title">
                    <span class="title-two">{{ item.regTime }}</span>
                </div>
                <div class="info-card">
                    <div class="flex text-box" >
                        <span class="text">处罚裁决文书号：</span>
                        <span class="text text-content">{{ item.decideFullNo || '无' }}</span>
                    </div>
                    <div class="flex text-box" @click="handleOpenPopup(item)" style="margin-top: 10px">
                        <span class="text">案由：</span>
                        <span class="text text-content">{{ item.caseOfAction }}</span>
                    </div>
                    <div class="flex" style="margin-top: 10px">
                        <span class="text">行政处罚时间：</span>
                        <span class="text ">
                            {{ item.punishDecideDate || '无' }}
                        </span>
                    </div>
                </div>
            </div>
        </van-list>
    </div>
    <van-popup v-model:show="scrollVisible" position="bottom" :title="popupData.regTime?.split(' ')[0] + ' 案由'"
        closeable @close="handlePopupClose">
        <div class="popup-content">{{ popupData.caseOfAction }}</div>
    </van-popup>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { http } from '@/utils/http'

const props = defineProps({
    yhytId: {
        type: String,
        required: true
    }
})

const dataList = ref([])
const loading = ref(false)
const finished = ref(false)
const scrollVisible = ref(false)
const popupData = ref({})
const page = ref(1)
const pageSize = 1000

const scrollToLowerSearch = async () => {
    try {
        loading.value = true
        const res = await http.get('/api/dingapp/illegalrecords/getListByCustCode', {
            params: {
                yhytId: props.yhytId,
                current: page.value,
                pageSize
            }
        })
        if (res.data) {
            dataList.value.push(...res.data)
            finished.value = true
        } else {
            finished.value = true
        }
    } catch (error) {
        console.error('获取违法记录失败:', error)
        finished.value = true
    } finally {
        loading.value = false
    }
}

const handleOpenPopup = (item) => {
    popupData.value = item
    scrollVisible.value = true
}

const handlePopupClose = () => {
    scrollVisible.value = false
}

onMounted(() => {
    scrollToLowerSearch()
})
</script>

<style lang="scss" scoped>
.wfjl-main {
    padding: 10px;
    background-color: #f5f5f5;
    height: 100%;
    overflow-y: auto;

    .title {
        margin: 10px 0;
        display: flex;
        justify-content: center;
        
        .title-two {
            font-size: 14px;
            color: #666;
        }
    }

    .info-card {
        background: #fff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;

        .flex {
            display: flex;
            align-items: flex-start;
        }

        .text {
            font-size: 14px;
            color: #333;

            &.text-content {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }

            &.text-time {
                color: #666;
            }
        }

        .text-box {
            cursor: pointer;
        }
    }
}

.popup-content {
    padding: 12px 14px 20px;
    height: 300px;
    overflow-y: auto;

}
</style>