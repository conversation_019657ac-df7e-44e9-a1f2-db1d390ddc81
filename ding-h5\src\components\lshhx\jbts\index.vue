<template>
  <div class="jbts-main">
    <van-empty v-if="dataList.length === 0" description="数据为空" />
    <van-list
      v-else
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="scrollToLowerSearch"
    >
      <div v-for="item in dataList" :key="item.id">
        <div class="title">
          <span class="title-two">{{ item.rollDate }}</span>
        </div>
        <div class="info-card">
          <div class="flex">
            <span class="text">所属类型：</span>
            <span class="text text-time">{{ item.eventType }}</span>
          </div>
          <div class="flex" style="margin-top: 10px">
            <span class="text">投诉内容：</span>
            <span class="text text-time">{{ item.eventContent }}</span>
          </div>
        </div>
      </div>
    </van-list>
  </div>
  <van-popup
    v-model:show="scrollVisible"
    position="bottom"
    :title="popupData.regTime?.split(' ')[0] + ' 案由'"
    closeable
    @close="handlePopupClose"
  >
    <div class="popup-content">{{ popupData.caseOfAction }}</div>
  </van-popup>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { http } from '@/utils/http'

const props = defineProps({
  licId: {
    type: String,
    required: true
  }
})

const dataList = ref([])
const loading = ref(false)
const finished = ref(false)
const scrollVisible = ref(false)
const popupData = ref({})
const page = ref(1)
const pageSize = 10



const scrollToLowerSearch = async () => {
  try {
    loading.value = true
    const res = await http.get('/api/dingapp/reportcomplaint/selectPage', {
      params: {
        licId: props.licId,
        current: page.value,
        pageSize
      }
    })
    if (res.data && res.data.records) {
      dataList.value.push(...res.data.records)
      if (dataList.value.length >= res.data.total) {
        finished.value = true
      }
      page.value++
    } else {
      finished.value = true
    }
  } catch (error) {
    console.error('获取投诉记录失败:', error)
    finished.value = true
  } finally {
    loading.value = false
  }
}

const handlePopupClose = () => {
  scrollVisible.value = false
}

onMounted(() => {
  scrollToLowerSearch()
})
</script>

<style lang="scss" scoped>
.jbts-main {
  padding: 10px;
  background-color: #f5f5f5;
  height: 100%;
  overflow-y: auto;

  .title {
    margin: 10px 0;
    display: flex;
    justify-content: center;

    .title-two {
      font-size: 14px;
      color: #666;
    }
  }

  .info-card {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;

    .flex {
      display: flex;
      align-items: flex-start;
    }

    .text {
      font-size: 14px;
      color: #333;

      &.text-time {
        flex: 1;
        word-break: break-all;
      }
    }
  }
}

.popup-content {
  padding: 12px 14px 20px;
  height: 300px;
  overflow-y: auto;
}
</style>