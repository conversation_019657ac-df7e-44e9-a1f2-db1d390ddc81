<template>
  <div class="search-select">
    <van-field
      v-model="selectedText"
      :label="label"
      :placeholder="placeholder"
      autosize
      type="textarea"
      readonly
      @click="showPopup = true"
    />
    <van-popup
      v-model:show="showPopup"
      position="bottom"
      round
      :style="{ height: '60%' }"
    >
      <div class="popup-container">
        <div class="popup-header">
          <van-search
            v-model="searchValue"
            :placeholder="searchPlaceholder"
            @search="onSearch"
          />
        </div>
        <div class="popup-content">
          <van-list
            :loading="loading"
            :finished="finished"
          >
            <van-cell
              v-for="item in filteredList"
              :key="item.value"
              :title="item.text"
              @click="onSelect(item)"
              clickable
            >
              <template #right-icon>
                <van-checkbox :model-value="item.selected" />
              </template>
            </van-cell>
          </van-list>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { http } from '@/utils/http'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '点击选择'
  },
  searchPlaceholder: {
    type: String,
    default: '请输入搜索关键词'
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  selectedItems: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

const showPopup = ref(false)
const searchValue = ref('')
const loading = ref(false)
const finished = ref(false)
const list = ref([])
const selectedMap = ref(new Map())
const firstLoad = ref(true)

watch(showPopup, (newVal) => {
  if (newVal) {
    onLoad()
  } else {
    // 在关闭时，根据当前的 selectedMap 更新父组件
    const selectedValuesOnClose = Array.from(selectedMap.value.keys())
    emit('update:modelValue', selectedValuesOnClose)
    emit('select', selectedValuesOnClose.map(value => {
      const found = list.value.find(item => item.value === value)
      return { value, text: found ? found.text : '未知', selected: true }
    }))
  }
})

const selectedText = computed(() => {
  // 获取所有选中的项的完整信息
  const selectedItems = Array.from(selectedMap.value.values())
  const selectedTexts = selectedItems.map(item => item.text).filter(Boolean)
  return selectedTexts.length ? selectedTexts.join(', ') : ''
})

const filteredList = computed(() => {
  if (!searchValue.value) return list.value
  return list.value.filter(item =>
    item.text.toLowerCase().includes(searchValue.value.toLowerCase())
  )
})

const onSearch = () => {
  list.value = []
  finished.value = false
  onLoad()
}

watch(() => props.selectedItems, (newItems) => {
  if (newItems && newItems.length > 0) {
    // 更新选中状态，保存完整的项信息
    selectedMap.value.clear()
    newItems.forEach(item => {
      selectedMap.value.set(item.value, {
        value: item.value,
        text: item.text,
        selected: true
      })
    })
    
    // 更新列表中的选中状态
    list.value = list.value.map(item => ({
      ...item,
      selected: selectedMap.value.has(item.value)
    }))
    
    // 如果是首次加载，将selectedItems添加到列表中
    if (firstLoad.value) {
      firstLoad.value = false
      const newItemsNotInList = newItems.filter(
        newItem => !list.value.some(item => item.value === newItem.value)
      )
      list.value = [...list.value, ...newItemsNotInList]
    }
  }
});

watch(searchValue, (newVal) => {
  list.value = []
  finished.value = false
  onLoad()
})

const onLoad = async () => {
  try {
    loading.value = true
    const res = await http.get(`/api/dingapp/license/getSelectionList`, {
      params: {
        name: searchValue.value
      } 
    })

    if (res.data) {
      finished.value = true
      const newList = res.data.map(item => ({
        value: item.id,
        text: item.companyName,
        selected: selectedMap.value.has(item.id)
      }))
      
      // 获取当前已选择的项的完整信息
      const currentlySelectedMap = new Map(Array.from(selectedMap.value.values()).map(item => [item.value, item]));

      // 创建合并后的列表
      const mergedList = [...newList];

      // 遍历当前已选择的项，如果不在 newList 中，则添加到 mergedList
      currentlySelectedMap.forEach(selectedItem => {
        if (!mergedList.some(item => item.value === selectedItem.value)) {
          mergedList.push({
            value: selectedItem.value,
            text: selectedItem.text,
            selected: true // 确保添加到列表中的已选项是选中的
          });
        } else {
          // 如果已存在于 newList 中，确保其 selected 状态是最新的
          const existingItem = mergedList.find(item => item.value === selectedItem.value);
          if (existingItem) {
            existingItem.selected = true;
          }
        }
      });
      
      list.value = mergedList
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const onSelect = (item) => {
  // 切换选中状态
  const isSelected = !selectedMap.value.has(item.value)
  
  // 更新 selectedMap，保存完整的项信息
  if (isSelected) {
    selectedMap.value.set(item.value, {
      value: item.value,
      text: item.text,
      selected: true
    })
  } else {
    selectedMap.value.delete(item.value)
  }

  // 更新列表中所有项的选中状态
  list.value = list.value.map(listItem => ({
    ...listItem,
    selected: selectedMap.value.has(listItem.value)
  }))

  // 准备发送给父组件的数据
  const selectedValues = Array.from(selectedMap.value.keys())
  const selectedItemsForEmit = Array.from(selectedMap.value.values())

  // 发送更新事件
  emit('update:modelValue', selectedValues)
  emit('select', selectedItemsForEmit)
}
</script>

<style lang="scss" scoped>
.search-select {
  .popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .popup-header {
      padding: 8px;
    }

    .popup-content {
      flex: 1;
      overflow-y: auto;
    }
  }
}

.search-select .van-field__body {
  white-space: normal !important;
  word-break: break-all;
}

</style>