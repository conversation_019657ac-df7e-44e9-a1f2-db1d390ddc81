import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLicenseStore = defineStore('license', () => {
  const currentLicense = ref(null)
  const explorationId = ref('')

  function setCurrentLicense(license) {
    currentLicense.value = license
  }

  function clearCurrentLicense() {
    currentLicense.value = null
  }

  function setExplorationId(id) {
    explorationId.value = id
  }

  function getExplorationId() {
    return explorationId.value
  }

  // 更新许可证位置信息
  function updateLicenseLocation(licenseId, latitude, longitude) {
    if (currentLicense.value && currentLicense.value.yhytLicenseVO && currentLicense.value.yhytLicenseVO.id === licenseId) {
      console.log('Store: 更新许可证位置信息', licenseId, latitude, longitude);
      currentLicense.value.yhytLicenseVO.latitude = latitude;
      currentLicense.value.yhytLicenseVO.longitude = longitude;
    }
  }

  return {
    currentLicense,
    explorationId,
    setCurrentLicense,
    clearCurrentLicense,
    setExplorationId,
    getExplorationId,
    updateLicenseLocation
  }
})