import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import * as dd from 'dingtalk-jsapi'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      component: () => import('../views/login/index.vue')
    },
    {
      path: '/map',
      component: () => import('../views/map/index.vue')
    },
    {
      path: '/mine',
      component: () => import('../views/mine/index.vue')
    },
    {
      path: '/survey',
      component: () => import('../views/survey/index.vue')
    },
    {
      path: '/comparison',
      component: () => import('../views/comparison/index.vue')
    },
    {
      path: '/search',
      component: () => import('../views/search/index.vue')
    },
    {
      path: '/pgsb-info',
      component: () => import('../views/pgsb-info/index.vue'),
      props: true
    },
    {
      path: '/pgsb-picture-info',
      component: () => import('../views/pgsb-picture-info/index.vue'),
      props: true
    },
    {
      path: '/lshhx',
      component: () => import('../views/lshhx/index.vue'),
      props: route => ({ xkzh: route.query.xkzh })
    },
    {
      path: '/todo',
      component: () => import('../views/todo/index.vue'),
    },
    {
      path: '/detail',
      component: () => import('../views/todo/detail.vue'),
    },
    {
      path: '/evidence-items',
      component: () => import('../views/evidence-items/index.vue'),
    },
    {
      path: '/evidence-items/detail',
      component: () => import('../views/evidence-items/detail.vue'),
    },
    {
      path: '/evidence-items/search',
      component: () => import('../views/evidence-items/search.vue'),
    },
    {
      path: '/evidence-items/detail-table',
      component: () => import('../views/evidence-items/detail-table.vue'),
    },
    {
      path: '/monitoring',
      component: () => import('../views/monitoring/index.vue'),
    },
    {
      path: '/survey-history',
      component: () => import('../views/survey-history/detail.vue'),
    }
  ]
})

router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 如果未登录且不是登录页面，重定向到登录页
  // if (!userStore.isLogin && to.path !== '/login') {
  //   next('/login')
  //   return
  // }

  // 特殊路由处理
  // if (to.path === '/lshhx' && !to.query.xkzh) {
  //   next('/search')
  //   return
  // }

  next()
})

export default router