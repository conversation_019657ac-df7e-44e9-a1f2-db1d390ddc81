<template>
    <div class="pgsb-main">
        <div v-if="!isView" @click="click">
            <div class="title">
                <span class="title-two">{{ todayData.identifyDate }}</span>
                <!-- <span class="time-segment-badge">当前时间段</span> -->
            </div>
            <div class="info-card">
                <div class="flex">
                    <span class="text">识别品规数：</span>
                    <span class="text">{{ todayData.identifyNum }}</span>
                </div>
                <div class="flex" style="margin-top: 10px; margin-bottom: 30px">
                    <span class="text">识别异常数：</span>
                    <span class="text text-time">{{ todayData.errorNum }}</span>
                </div>
                <van-divider />
                <div class="btn-container">
                    <div class="pgsb-btn" @click="handleStartRecognition">开始识别</div>
                </div>
            </div>
        </div>

        <van-empty v-if="dataList.length === 0 && isView" description="数据为空" />

        <van-list v-if="isView && dataList.length > 0" v-model:loading="loading" :finished="finished"
            :finished-text="scrollToast || '没有更多了'" @load="scrollToLowerLoad">
            <div v-for="item in dataList" :key="item.identifyDate">
                <div class="title">
                    <span class="title-two">{{ item.identifyDate }}</span>
                </div>
                <div class="info-card">
                    <div class="flex">
                        <span class="text">识别品规数：</span>
                        <span class="text">{{ item.identifyNum }}</span>
                    </div>
                    <div class="flex" style="margin-top: 10px; margin-bottom: 30px">
                        <span class="text">识别异常数：</span>
                        <span class="text text-time">{{ item.errorNum }}</span>
                    </div>
                    <van-divider />
                    <div class="btn-container">
                        <div class="pgsb-btn" @click="handleViewClick(item)">查看详情</div>
                    </div>
                </div>
            </div>
        </van-list>

        <div v-if="!isView && dataList.length > 0">
            <div v-for="item in dataList" :key="item.identifyDate">
                <div class="title">
                    <span class="title-two">{{ item.identifyDate }}</span>
                </div>
                <div class="info-card">
                    <div class="flex">
                        <span class="text">识别品规数：</span>
                        <span class="text">{{ item.identifyNum }}</span>
                    </div>
                    <div class="flex" style="margin-top: 10px; margin-bottom: 30px">
                        <span class="text">识别异常数：</span>
                        <span class="text text-time">{{ item.errorNum }}</span>
                    </div>
                    <van-divider />
                    <div class="btn-container">
                        <div class="pgsb-btn" @click="handleViewClick(item)">查看详情</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { http } from '@/utils/http'
import { getWeekByDate } from '@/utils/dateUtil'
import { showSuccessToast, showFailToast  } from 'vant'
import { useLicenseStore } from '@/stores/license'

// 确定当前时间属于哪个时间段
const getCurrentTimeSegment = () => {
  const now = new Date()
  const hour = now.getHours()

  // 定义时间段（每2小时一个时间段）
  const timeSegments = [
    { start: 0, end: 2, label: '00:00-02:00' },
    { start: 2, end: 4, label: '02:00-04:00' },
    { start: 4, end: 6, label: '04:00-06:00' },
    { start: 6, end: 8, label: '06:00-08:00' },
    { start: 8, end: 10, label: '08:00-10:00' },
    { start: 10, end: 12, label: '10:00-12:00' },
    { start: 12, end: 14, label: '12:00-14:00' },
    { start: 14, end: 16, label: '14:00-16:00' },
    { start: 16, end: 18, label: '16:00-18:00' },
    { start: 18, end: 20, label: '18:00-20:00' },
    { start: 20, end: 22, label: '20:00-22:00' },
    { start: 22, end: 24, label: '22:00-24:00' }
  ]

  // 查找当前时间所属的时间段
  const currentSegment = timeSegments.find(segment => hour >= segment.start && hour < segment.end)

  return currentSegment || timeSegments[0] // 默认返回第一个时间段
}


const props = defineProps({
    isView: {
        type: Boolean,
        default: false
    },
    licData: {
        type: Object,
        required: true
    }
})

const router = useRouter()
const dataList = ref([])
const loading = ref(false)
const finished = ref(false)
const formPage = ref({
    pageNo: 1,
    pageSize: 15,
    total: 0
})
const scrollToast = ref('')

// 获取当前时间段
const currentTimeSegment = ref(getCurrentTimeSegment())

// 创建一个计算属性，用于判断是否是当前时间段
const isCurrentTimeSegment = (dateTimeStr) => {
    if (!dateTimeStr) return false

    // 从日期字符串中提取时间段部分
    const timeSegmentMatch = dateTimeStr.match(/(\d{2}:\d{2}-\d{2}:\d{2})/)
    if (!timeSegmentMatch) return false

    return timeSegmentMatch[1] === currentTimeSegment.value.label
}

const todayData = ref({
    identifyDate: new Date().toISOString().split('T')[0] + ' ' + currentTimeSegment.value.label + ' ' + getWeekByDate(new Date().toISOString().split('T')[0]),
    identifyNum: 0,
    errorNum: 0,
    timeSegment: currentTimeSegment.value.label
})

const licenseStore = useLicenseStore()

const handleStartRecognition = () => {
    router.push({
        path: '/pgsb-info',
        query: {
            type: 'survey',
            date: todayData.value.identifyDate,
            licNo: licenseStore.currentLicense.yhytLicenseVO.licNo,
            explorationId: licenseStore.explorationId,
            timeSegment: currentTimeSegment.value.label
        }
    })
}

const handleViewClick = (item) => {
    router.push({
        path: '/pgsb-info',
        query: {
            type: 'view',
            date: item.identifyDate,
            licNo: props.licData.licNo,
            explorationId: item.explorationId
        }
    })
}

const loadData = async () => {
    try {
        const res = await http.get('/api/dingapp/itemIdentifyResults/countList', {
            params: {
                licId: props.licData.id,
                current: formPage.value.pageNo,
                size: formPage.value.pageSize
            }
        })

        if (res && res.data) {
            let list = [];
            if (formPage.value.pageNo > 1) {
                list = [...dataList.value];
            }

            const resData = res.data.records?.map(item => ({
                identifyDate: item.identifyDate + ' ' + item.timeSegment + ' ' + getWeekByDate(item.identifyDate),
                identifyNum: item.identifyNum,
                errorNum: item.errorNum,
                explorationId: item.explorationId
            })) || [];

            list.push(...resData);
            formPage.value.total = res.data.total || 0;
            formPage.value.pageSize = res.data.size || 15;
            formPage.value.pageNo = res.data.current || 1;
            dataList.value = list;

            // 如果是第一页，处理今天的数据
            if (!props.isView && formPage.value.pageNo === 1 && list.length > 0) {
                // 找到今天当前时间段的数据
                const todayCurrentSegmentIndex = list.findIndex(item => {
                    const itemDate = item.identifyDate.split(' ')[0];
                    const today = new Date().toISOString().split('T')[0];
                    return itemDate === today && isCurrentTimeSegment(item.identifyDate);
                });

                if (todayCurrentSegmentIndex !== -1) {
                    // 如果找到当前时间段的数据，将其提取到todayData
                    todayData.value = list[todayCurrentSegmentIndex];
                    // 移除这条数据，其他数据保留在列表中
                    list.splice(todayCurrentSegmentIndex, 1);
                    dataList.value = list;
                } else {
                    // 如果没有找到当前时间段的数据，保持todayData为初始值
                    // 所有数据都显示在列表中
                    dataList.value = list;
                }
            }

            loading.value = false;
            if (dataList.value.length >= formPage.value.total) {
                finished.value = true;
            }
        }
    } catch (error) {
        console.error('获取识别记录失败:', error)
        loading.value = false;
        finished.value = true;
    }
}

const scrollToLowerLoad = () => {
    if (formPage.value.pageNo * formPage.value.pageSize >= formPage.value.total) {
        if (formPage.value.pageNo === 1 && formPage.value.total < 15) {
            scrollToast.value = '';
            return;
        }
        scrollToast.value = '已经到底啦';
        return;
    }

    formPage.value.pageNo += 1;
    loading.value = true;
    loadData();
}

onMounted(async () => {
    await loadData();
})
</script>

<style lang="scss" scoped>
.pgsb-main {
    flex: 1;
    padding: 10px 16px 16px;
    background-color: #f5f5f5;
    height: 100%;
    overflow-y: auto;

    .title {
        margin: 10px 0;
        font-size: 14px;

        .title-two {
            padding-left: 10px;
            display: inline-block;
            color: #666;
        }

        .time-segment-badge {
            display: inline-block;
            background-color: #1c88f9;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }
    }

    .info-card {
        width: 100%;
        background: #fff;
        border-radius: 15px;
        box-sizing: border-box;
        margin: 10px auto 16px;
        padding: 16px 16px 10px;
        min-height: 130px;
        box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);

        .flex {
            display: flex;
            align-items: flex-start;
        }

        .text {
            font-size: 14px;
            color: #333;
            flex: 1;
            min-width: 0;
            white-space: normal;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;

            &.text-time {
                color: #666;
                -webkit-line-clamp: 2;
            }
        }

        .btn-container {
            display: flex;
            justify-content: flex-end;
            width: 100%;
            margin-top: 10px;
        }

        .pgsb-btn {
            background-color: #1c88f9;
            color: #fff;
            text-align: center;
            border-radius: 7px;
            padding: 6px 15px;
            font-size: 14px;
            cursor: pointer;

            &:active {
                opacity: 0.8;
            }
        }
    }
}
</style>