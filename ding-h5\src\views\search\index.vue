<template>
    <div class="main-app">
        <div class="search-panel">
            <div class="search-box-wrapper">
                <div class="search-box">
                    <van-search v-model="searchParam" placeholder="搜索法人姓名、许可证号、名称、地址" class="search-bar"
                        @input="handleSearchInputChange" @search="handleSearchBtnClick">
                        <template #right-icon>
                            <van-icon name="scan" size="24" @click="handleSearchBarScan" />
                        </template>
                    </van-search><div class="search-btn" @click="handleSearchBtnClick">搜索</div>
                </div>

                <div class="format-param-box">
                    <div class="format-label">业态：</div>
                    <van-dropdown-menu class="format-dropdown">
                        <van-dropdown-item v-model="formatParamValue" :options="formatDropdownOptions" @change="handleFormatChange" />
                    </van-dropdown-menu>
                </div>
            </div>
        </div>
        <div class="user-location-content">
            <div class="location-label">当前位置：</div>
            <div class="location-value">{{ currentAddress }}</div>
        </div>
        <!-- Removed format popup as we're using dropdown menu instead -->

        <div class="list-container" ref="listContainerRef">
          <van-list v-model:loading="loading" :finished="finished" finished-text="已经到底啦" @load="onLoad" :immediate-check="true" :offset="100" :scroller="listContainerRef">
            <div class="search-form-list" v-if="searchResultList.length > 0">
                <div class="search-form" v-for="(item, index) in searchResultList" :key="index">
                    <div class="search-form-box" @click="handleFormBtn(item)">
                        <div class="image">
                            <img class="vista-image"
                                :src="item.photoPathList?.length > 0 ? item.photoPathList[0].filthPath : '/picture-icon.svg'"
                                @click.stop="previewVistaPic(item)" />
                        </div>
                        <div class="text">
                          <div class="detail-address">
                                <div class="detail-address-text">{{ item.companyName }}</div>
                            </div>
                            <div class="detail-address">
                                <div class="detail-address-text">{{ item.businessAddr }}</div>
                            </div>
                            <div class="detail-lic-no">
                                <div class="detail-lic-no-text">法人代表：{{ item.yhytLicenseVO.managerName }}</div>
                            </div>
                            <div class="detail-lic-no">
                                <div class="detail-lic-no-text">许可证：{{ item.licNo }}</div>
                            </div>
                            <div class="detail-distance">
                                <div class="detail-distance-text">距您{{ item.distance || 0 }}米</div>
                            </div>
                        </div>
                    </div>
                    <div class="search-form-btn">
                        <div class="retailers" @click="toLshhx(item)">
                            <img class="retailers-image" src="@/assets/retailers-icon.svg" />
                            <div class="retailers-text">零售户画像</div>
                        </div>
                    </div>
                </div>
            </div>
            <van-empty v-else description="暂无搜索结果" />
        </van-list>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { http } from '@/utils/http'
import { showSuccessToast, showFailToast, showImagePreview } from 'vant'
import * as dd from 'dingtalk-jsapi'
import { calculateDistance } from '@/utils/distanceUtil'
import { useLicenseStore } from '@/stores/license'
import { gcj02ToWgs84, wgs84ToGcj02 } from '@/utils/coordinate'

const route = useRoute()
const router = useRouter()
const licenseStore = useLicenseStore()

const currentLon = ref(null)
const currentLat = ref(null)
const currentAddress = ref('')
const searchParam = ref('')
const loading = ref(false)
const finished = ref(false)
const searchResultList = ref([])
const formatParamList = ref([])
const formatParamValue = ref('')
const formatDropdownOptions = ref([])
const timer = ref(null)
const listContainerRef = ref(null)

const formPage = ref({
  pageNo: 1,
  pageSize: 15,
  total: 10
})

const searchList = async () => {
  //map页过来的是wgs84坐标系，需要转换为gcj02坐标系,供后端计算距离
  // 使用临时变量存储转换后的坐标，不修改原始值
  const [mglng, mglat] = wgs84ToGcj02(currentLon.value, currentLat.value)

  // 仅在日志和API请求中使用转换后的坐标
  console.log('请求数据参数:', {
    searchParam: searchParam.value,
    formatParam: formatParamValue.value,
    current: formPage.value.pageNo,
    size: formPage.value.pageSize,
    longitude: mglng,
    latitude: mglat
  })

  try {
    const res = await http.get('/api/dingapp/license/selectYhytPage', {
      params: {
        searchParam: searchParam.value,
        formatParam: formatParamValue.value,
        current: formPage.value.pageNo,
        size: formPage.value.pageSize,
        longitude: mglng,
        latitude: mglat
      }
    })

    if (res?.data) {
      const data = res.data
      console.log('获取数据成功:', {
        total: data.total,
        current: data.current,
        size: data.size,
        records: data.records?.length || 0
      })

      // 判断是否是加载更多数据
      let list = formPage.value.pageNo > 1 ? [...searchResultList.value] : []

      data.records.forEach(item => {
        item.distance = Math.round(item.distance)
        list.push(item)
      })

      searchResultList.value = list
      formPage.value.total = data.total
      formPage.value.pageSize = data.size
      formPage.value.pageNo = data.current

      loading.value = false
      if (list.length >= data.total) {
        console.log('数据已全部加载完毕')
        finished.value = true
      }
    }
  } catch (error) {
    showFailToast('获取数据失败')
    console.error('获取数据失败:', error)
    loading.value = false
  }
}

const handleSearchBarScan = () => {
  dd.scan({
    type: 'qr',
    onSuccess: (res) => {
      const text = res.text
      if (!text) {
        showFailToast('未识别到二维码')
        return
      }

      const licenseNumberMatch = text.match(/许可证号[:：]\s*(\d+)/)

      if (licenseNumberMatch) {
        searchParam.value = licenseNumberMatch[1]
        handleSearchBtnClick()
      } else {
        showFailToast('未识别许可证号')
      }
    },
    onFail: (err) => {
      showFailToast('扫码失败')
      console.error('扫码失败:', err)
    }
  })
}

const handleSearchInputChange = () => {
  clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    handleSearchBtnClick()
  }, 500)
}

const handleSearchBtnClick = () => {
  // 清除之前的定时器，防止重复请求
  clearTimeout(timer.value)
  timer.value = null
  loading.value = true  // 设置loading状态
  formPage.value.pageNo = 1
  finished.value = false
  searchResultList.value = []
  searchList()
}

const loadGroupFormatList = async () => {
  try {
    const res = await http.get('/api/dingapp/license/getFormatList')
    if (res?.data) {
      formatParamList.value = res.data.map(item => ({
        text: item,
        value: item
      }))

      // 为下拉菜单准备选项
      formatDropdownOptions.value = [
        { text: '全部', value: '' }
      ]

      // 添加服务器返回的数据，排除可能存在的“全部”选项
      res.data.forEach(item => {
        if (item && item !== '全部') {
          formatDropdownOptions.value.push({
            text: item,
            value: item
          })
        }
      })

      console.log('formatParamList', formatParamList.value)
      if (!formatParamValue.value) {
        formatParamValue.value = ''
      }
    }
  } catch (error) {
    showFailToast('获取业态列表失败')
    console.error('获取业态列表失败:', error)
  }
}

const onLoad = async () => {
  try {
    console.log('触发加载更多数据', {
      pageNo: formPage.value.pageNo,
      pageSize: formPage.value.pageSize,
      total: formPage.value.total
    })

    // 检查是否已经加载完所有数据
    if (formPage.value.pageNo * formPage.value.pageSize >= formPage.value.total) {
      console.log('数据已加载完毕')
      finished.value = true
      loading.value = false
      return
    }

    // 增加页码并加载下一页数据
    formPage.value.pageNo += 1
    console.log('加载第 ' + formPage.value.pageNo + ' 页数据')
    await searchList()
  } catch (error) {
    console.error('加载数据失败:', error)
    loading.value = false
    finished.value = true
    showFailToast('加载数据失败，请稍后重试')
  }
}

const previewVistaPic = (item) => {
  if (item && item.photoPathList && item.photoPathList.length>0 && item.photoPathList[0].filthPath) {
        showImagePreview({
            images: [item.photoPathList[0].filthPath],
            closeable: true
        })
    }else{
        showFailToast('暂无图片')
    }

}

const handleFormBtn = (item) => {
  console.log('item', item)
  if (!item.longitude || !item.latitude) {
    showFailToast('位置信息不完整')
    return
  }

  const locationData = {
    longitude: item.longitude,
    latitude: item.latitude,
    GCJ02Lon:item.yhytLicenseVO.longitude,
    GCJ02Lat:item.yhytLicenseVO.latitude,
    markerId: item.id,
    address: item.businessAddr
  }
  localStorage.setItem('targetLocation', JSON.stringify(locationData))
  router.back()
  // router.push('/map')
}

const toLshhx = (item) => {
    console.log('item', item)
  if (!item.licNo) {
    showFailToast('许可证号不能为空')
    return
  }

  licenseStore.setCurrentLicense(item)
  router.push(`/lshhx?view=true&xkzh=${item.licNo}`)
}

const handleFormatChange = () => {
  console.log('选择业态:', formatParamValue.value)
  localStorage.setItem('formatParamValue', formatParamValue.value)
  handleSearchBtnClick()
}

// Removed unused functions

onMounted(() => {
  const query = route.query
  currentAddress.value = query.currentAddress
  currentLon.value = parseFloat(query.currentLon)
  currentLat.value = parseFloat(query.currentLat)

  // 处理搜索参数，优先使用 keyword 参数（从地图页扫码跳转）
  if (query.keyword) {
    searchParam.value = query.keyword
  } else if (query.searchParam) {
    searchParam.value = query.searchParam
  }

  // 先获取业态列表，再设置已保存的业态值
  loadGroupFormatList().then(() => {
    const storedFormatParam = localStorage.getItem('formatParamValue')
    if (storedFormatParam) {
      formatParamValue.value = storedFormatParam
    }

    // 如果有搜索关键词，自动触发搜索
    if (searchParam.value) {
      handleSearchBtnClick()
    } else {
      searchList()
    }
  })
})
</script>

<style lang="scss" scoped>
.main-app {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #FFFFFF;
  overflow: hidden;
  position: relative;
}

.search-panel {
  display: flex;
  flex-direction: column;
  padding: 10px 7px 0 7px;
  flex-shrink: 0; /* 确保不会缩小 */
  z-index: 10; /* 确保在列表上方 */
  background-color: #FFFFFF;
}

.search-box-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.search-box {
  display: flex;
  height: 36px;
  width: 100%;
  justify-content: space-between;
  align-items: center;

  .search-bar {
    flex: 1;
    padding: 0;
    height: 36px;

    :deep(.van-search) {
      height: 36px;
      padding: 0;
    }
    :deep(.van-search__content) {
      border-radius: 20px 0 0 20px;
      height: 36px;
    }
  }
}

.search-btn {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  width: 60px;
  background-color: #1989fa;
  color: white;
  border-radius: 0 10px 10px 0;
  font-size: 14px;
  margin-left: 0;
  padding: 0;
}

.format-param-box {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 5px;
  padding-left: 10px;
  font-size: 14px;
  color: #666;
}

.format-label {
  font-weight: bold;
  margin-right: 5px;
}

.format-dropdown {
  flex: 1;
  height: 35px;

  :deep(.van-dropdown-menu__bar) {
    height: 35px;
    box-shadow: none;
    background-color: transparent;
  }

  :deep(.van-dropdown-menu__item) {
    justify-content: flex-start;
    padding-left: 0;
  }
}

.user-location-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start; /* 改为顶部对齐，以便于多行文本对齐 */
  width: 100%;
  color: #9295A0;
  font-size: 14px;
  margin: 5px 0;
  padding: 8px 16px; /* 增加上下内边距，为多行文本提供空间 */
  flex-shrink: 0; /* 确保不会缩小 */
  background-color: #FFFFFF;
  box-sizing: border-box;

  .location-label {
    flex-shrink: 0;
    white-space: nowrap;
    line-height: 1.4; /* 与文本内容保持相同的行高 */
    padding-top: 1px; /* 微调对齐 */
  }

  .location-value {
    flex: 1;
    word-break: break-all; /* 允许在任意字符间断行 */
    white-space: normal; /* 允许换行 */
    padding-right: 16px; /* 右边留出空间 */
    line-height: 1.4; /* 增加行高以提高可读性 */
  }
}

.list-container {
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 140px); /* 增加高度减去值，以适应可能的多行文本 */
  -webkit-overflow-scrolling: touch; /* 增强移动端滑动体验 */
  position: relative;

  :deep(.van-list) {
    min-height: 100%;
  }
}

.search-form-list {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.search-form {
  width: 100%;
  padding: 5px 0;
  border-bottom: 1px solid #F2F3F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  &:last-child {
    border: none;
  }
}

.search-form-box {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 8px 0;

  .image {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 55px;
    padding-right: 8px;

    .vista-image {
      width: 54px;
      height: 51px;
      border-radius: 5px;
    }
  }

  .text {
    flex-grow: 1;
    color: #9295A0;
    margin-left: 8px;

    .detail-address,
    .detail-lic-no,
    .detail-distance {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      line-height: 1.2;
      margin-top: 4px;

      &-text {
        flex-grow: 1;
      }
    }

    .detail-address:first-child {
      .detail-address-text {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .detail-address:not(:first-child),
    .detail-lic-no,
    .detail-distance {
      font-size: 14px;
      color: #9295A0;
    }
  }
}

.search-form-btn {
  flex-shrink: 0;
}

.retailers {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 65px;
  padding-left: 8px;

  &-image {
    width: 28px;
    height: 28px;
    margin-bottom: 4px;
  }

  &-text {
    color: #9295A0;
    font-size: 12px;
  }
}
</style>