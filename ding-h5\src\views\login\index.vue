<template>
  <div class="login-container">
    <div class="login-box">
      <img src="/src/assets/logo.png" alt="logo" class="logo">
      <h2>专卖稽查智能辅助系统</h2>
      <van-button type="primary" block @click="handleLogin" :loading="loading">钉钉授权登录</van-button>
    </div>
    <van-dialog v-model:show="loginFailDialog" title="登录失败" :show-cancel-button="false" @confirm="handleFailDialogClose">
      <p>登录失败，请稍后重试</p>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const loginFailDialog = ref(false)

const handleLogin = async () => {
  loading.value = true
  try {
    await userStore.login()
    // 登录成功后的跳转由handleLoginSuccess函数处理
  } catch (error) {
    console.error('登录失败:', error)
    loginFailDialog.value = true
    loading.value = false
  }
}

const handleFailDialogClose = () => {
  loginFailDialog.value = false
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f8fa;

  .login-box {
    width: 80%;
    max-width: 300px;
    text-align: center;

    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }

    h2 {
      margin-bottom: 30px;
      color: #323233;
      font-size: 20px;
    }
  }
}
</style>