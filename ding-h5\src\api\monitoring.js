import { http } from '@/utils/http'

/**
 * 获取中队列表
 * 使用部门接口获取中队数据
 * @param {string} deptId 部门ID，用于筛选特定部门
 * @returns {Promise<Array>} 中队列表
 */
export function getDeptList() {
  return http.get('/api/blade-system/dept/list-by-parent-id?parentId=1916834180384563201')
}

/**
 * 获取中队下的人员列表
 * @param {string} deptId 中队ID
 * @returns {Promise<Array>} 人员列表
 */
export function getUserList(deptId) {
  return http.get(`/api/blade-system/user/list-by-dept?deptId=${deptId}`)
}

/**
 * 获取监控数据
 * @param {Object} params 查询参数
 * @param {string} params.timeRange 时间范围：day, week, month, quarter, year
 * @param {string|null} params.deptId 中队ID，为null时查询所有中队
 * @param {string|null} params.userId 人员ID，为null时查询所有人员
 * @returns {Promise<Object>} 监控数据
 */
export function getMonitoringData(params) {
  return http.get('/api/dingapp/monitoring/data', { params })
}

/**
 * 获取检客户统计数据
 * @param {Object} params 查询参数
 * @param {string} params.timeRange 时间范围：day, week, month, quarter, year
 * @param {string|null} params.deptId 中队ID，为null时查询所有中队
 * @param {string|null} params.userId 人员ID，为null时查询所有人员
 * @returns {Promise<Object>} 检客户统计数据
 */
export function getInspectionData(params) {
  return http.get('/api/dingapp/monitoring/inspection-data', { params })
}

/**
 * 获取品规识别统计数据
 * @param {Object} params 查询参数
 * @param {string} params.timeRange 时间范围：day, week, month, quarter, year
 * @param {string|null} params.deptId 中队ID，为null时查询所有中队
 * @param {string|null} params.userId 人员ID，为null时查询所有人员
 * @returns {Promise<Object>} 品规识别统计数据
 */
export function getRecognitionData(params) {
  return http.get('/api/dingapp/monitoring/recognition-data', { params })
}

/**
 * 获取正常/异常烟统计数据
 * @param {Object} params 查询参数
 * @param {string} params.timeRange 时间范围：day, week, month, quarter, year
 * @param {string|null} params.deptId 中队ID，为null时查询所有中队
 * @param {string|null} params.userId 人员ID，为null时查询所有人员
 * @returns {Promise<Object>} 正常/异常烟统计数据
 */
export function getCigaretteData(params) {
  return http.get('/api/dingapp/monitoring/cigarette-data', { params })
}

/**
 * 获取人员排名数据
 * @param {Object} params 查询参数
 * @param {string} params.timeRange 时间范围：day, week, month, quarter, year
 * @param {string} params.deptId 中队ID，必须指定中队
 * @returns {Promise<Array>} 人员排名数据
 */
export function getPersonnelRanking(params) {
  return http.get('/api/dingapp/monitoring/personnel-ranking', { params })
}
