<template>
    <div class="pgsb-picture-info" style="padding-bottom: 40px">
        <van-cell-group style="padding: 0 16px">
            <img :src="imgurl" style="width: calc(100% - 32px); height: 200px; display: inline-block; margin: 0 16px; cursor: pointer;" @click="clickImg" @error="handleImageError" />
        </van-cell-group>

        <van-cell-group title="对碰结果">
            <div class="table-container">
                <div class="table-header">
                    <div class="col-item" style="width: 60px">序号</div>
                    <div class="col-item" style="width: 130px">品规码</div>
                    <div class="col-item" style="width: 300px">品规</div>
                    <div class="col-item" style="width: 180px">嫌疑类别</div>
                </div>
                <div class="table-body">
                    <template v-if="collisionResult.length">
                        <div v-for="(item, index) in collisionResult" :key="index" class="table-row">
                        <div class="col-item" style="width: 60px">{{ index + 1 }}</div>
                        <div class="col-item" style="width: 130px">{{ item.itemCode }}</div>
                        <div class="col-item" style="width: 300px">{{ item.itemName }}</div>
                        <div class="col-item" :class="getCollisionTypeClass(item.collisionType)" style="width: 180px">{{ item.collisionType }}</div>
                    </div>
                    </template>
                    <van-empty v-else description="暂无对碰结果" image-size="100" />
                </div>
            </div>
        </van-cell-group>

        <van-cell-group title="识别评价">
            <!-- <div class="table-container">
                <div class="table-header">
                    <div class="col-item" style="width: 60px">序号</div>
                    <div class="col-item" style="width: 130px">品规码</div>
                    <div class="col-item" style="width: 500px">品规</div>
                </div>
                <div class="table-body">
                    <template v-if="identificationResult.length">
                        <div v-for="(item, index) in identificationResult" :key="index" class="table-row">
                        <div class="col-item" style="width: 60px">{{ index + 1 }}</div>
                        <div class="col-item" style="width: 130px">{{ item.itemCode }}</div>
                        <div class="col-item" style="width: 500px">{{ item.itemName }}</div>
                    </div>
                    </template>
                    <van-empty v-else description="暂无识别结果"  image-size="100" />
                </div>
            </div> -->
            <div style="display: flex; align-items: center; padding: 0 16px 20px">
                <span style="line-height: 40px">识别结果较差：</span>
                <van-radio-group v-model="checked" direction="horizontal" @change="checkOnChange">
                    <van-radio :name="true">是</van-radio>
                    <van-radio :name="false">否</van-radio>
                </van-radio-group>
            </div>
        </van-cell-group>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { http } from '@/utils/http'
import { Toast, showImagePreview  } from 'vant'

const route = useRoute()

const imgurl = ref('')
const identifyId = ref('')
const checked = ref(true)
const throttleFlag = ref(false)

const handleImageError = () => {
    Toast('图片加载失败')
}

const clickImg = () => {
    if (imgurl.value) {
        showImagePreview({
            images: [imgurl.value],
            closeable: true
        })
    }
}

const getCollisionTypeClass = (type) => {
    switch (type) {
        case '重复':
            return 'collision-type-duplicate'
        case '非烟':
            return 'collision-type-error'
        case '正常':
            return 'collision-type-normal'
        default:
            return ''
    }
}

const collisionResult = ref([])
const collistionColumn = ref([
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        width: 60
    },
    {
        title: '品规码',
        dataIndex: 'itemCode',
        key: 'name',
        width: 180
    },
    {
        title: '品规',
        dataIndex: 'itemName',
        key: 'name',
        width: 300
    },
    {
        title: '嫌疑类别',
        dataIndex: 'collisionType',
        key: 'name',
        width: 180
    }
])

const identificationResult = ref([])
const identificationColumn = ref([
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        width: 60
    },
    {
        title: '品规码',
        dataIndex: 'itemCode',
        key: 'name',
        width: 200
    },
    {
        title: '品规',
        dataIndex: 'itemName',
        key: 'name',
        width: 500
    }
])

const getDataList = async () => {
    try {
        const res = await http.get('/api/dingapp/itemIdentifyResults/list', {
            params: {
                identifyId: identifyId.value
            }
        })

        identificationResult.value = res.data

        const errorData = [...res.data]
        errorData.sort((a, b) => {
            if (a.collisionType > b.collisionType) return -1
            if (a.collisionType < b.collisionType) return 1
            if (a.itemCode < b.itemCode) return -1
            if (a.itemCode > b.itemCode) return 1
            return 0
        })

        collisionResult.value = errorData
    } catch (error) {
        showFailToast('获取数据失败')
        console.error('获取数据失败:', error)
    }
}

const getIdentifyDetail = async () => {
    try {
        const res = await http.get('/api/dingapp/itemIdentify/detail', {
            params: {
                id: identifyId.value
            }
        })

        if (res.data) {
            checked.value = res.data.isRecognitionBad
        }
    } catch (error) {
        showFailToast('获取识别详情失败')
        console.error('获取识别详情失败:', error)
    }
}

const checkOnChange = async (value) => {
    if (throttleFlag.value) return

    throttleFlag.value = true
    checked.value = value

    try {
        await http.get('/api/dingapp/itemIdentify/updateIsBad', {
            params: {
                id: identifyId.value,
                isRecognitionBad: value
            }
        })
    } catch (error) {
        showFailToast('更新状态失败')
        console.error('更新状态失败:', error)
    }

    setTimeout(() => {
        throttleFlag.value = false
    }, 500)
}

onMounted(() => {
    const { url, id } = route.query
    if (url && id) {
        imgurl.value = url
        identifyId.value = id
        getDataList()
        getIdentifyDetail()
    }
})
</script>

<style lang="scss" scoped>
.pgsb-picture-info {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 0 0 40px;

    .table-container {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        .table-header {
            display: flex;
            background-color: #f7f8fa;
            border-bottom: 1px solid #ebedf0;

            .col-item {
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 500;
                color: #323233;
                white-space: nowrap;
            }
        }

        .table-body {
            .table-row {
                display: flex;
                border-bottom: 1px solid #ebedf0;

                &:last-child {
                    border-bottom: none;
                }

                .col-item {
                    padding: 12px 16px;
                    font-size: 14px;
                    color: #646566;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    &.collision-type-duplicate {
                        color: #ff9800;
                    }

                    &.collision-type-error {
                        color: #f44336;
                    }

                    &.collision-type-normal {
                        color: #4caf50;
                    }
                }
            }

            .table-row:nth-child(even) {
                background-color: #fafafa;
            }
        }
    }
}
</style>