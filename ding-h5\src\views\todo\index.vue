<template>
  <div class="todo-container">
    <div class="search-panel">
      <div class="search-box-wrapper">
        <div class="search-box">
          <van-search v-model="searchValue" placeholder="搜索待办事项" class="search-bar">
            <template #right-icon>
              <van-icon name="scan" size="24" @click="handleSearchBarScan" />
            </template>
          </van-search>
          <div class="search-btn" @click="handleSearch">
            搜索
          </div>
        </div>
      </div>
    </div>
    <van-empty v-if="todoList.length === 0" description="暂无待办事项" />
    <van-list v-else v-model:loading="loading" :finished="finished" :finished-text="scrollToast || '没有更多了'" @load="scrollToLowerLoad">
      <div class="todo-list">
        <div v-for="item in todoList" :key="item.id" class="todo-item">
          <div class="todo-content" @click="toDetail(item)" >
            <div class="todo-title">{{ item.eventTitle }}</div>
            <div class="todo-desc">{{ item.eventType }}</div>
            <div class="todo-info">
              <span class="todo-time">{{ item.rollDate }}</span>
              <!-- <span :class="['todo-status', item.status === '待处理' ? 'pending' : 'done']">{{ item.status }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </van-list>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Icon, List, Empty } from 'vant'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { http } from '@/utils/http'

const router = useRouter()
const searchValue = ref('')
const loading = ref(false)
const finished = ref(false)
const scrollToast = ref('')
const todoList = ref([])

const formPage = ref({
  pageNo: 1,
  pageSize: 15,
  total: 0
})

const handleSearchBarScan = () => {
  // 处理扫描功能
}

const handleSearch = () => {
  formPage.value.pageNo = 1
  todoList.value = []
  finished.value = false
  getTodoList()
}

const toDetail = (item) => {
  router.push('/detail?id='+item.id)
}

const userStore = useUserStore()
const userId = userStore.userInfo?.user_id

const getTodoList = async () => {
  try {
    const res = await http.get('/api/dingapp/reportcomplaint/page-user', {
      params: {
        // userId: '1887793455687979010',
        userId: userId,
        current: formPage.value.pageNo,
        size: formPage.value.pageSize,
        keyword: searchValue.value
      }
    })
    
    if (res.data) {
      let list = []
      if (formPage.value.pageNo > 1) {
        list = [...todoList.value]
      }

      const resData = res.data.records || []
      list.push(...resData)

      formPage.value.total = res.data.valuetotal || 0
      formPage.value.pageSize = res.data.size || 15
      formPage.value.pageNo = res.data.current || 1
      todoList.value = list

      loading.value = false
      if (todoList.value.length >= formPage.value.total) {
        finished.value = true
        if (formPage.value.pageNo === 1 && formPage.value.total < 15) {
          scrollToast.value = ''
        } else {
          scrollToast.value = '已经到底啦'
        }
      }
    }
  } catch (error) {
    console.error('获取待办列表失败:', error)
    loading.value = false
    finished.value = true
  }
}

const scrollToLowerLoad = () => {
  if (formPage.value.pageNo * formPage.value.pageSize >= formPage.value.total) {
    return
  }

  formPage.value.pageNo += 1
  loading.value = true
  getTodoList()
}

onMounted(async () => {
  await getTodoList()
})
</script>

<style lang="scss" scoped>
.todo-container {
  min-height: 100vh;
  background-color: #f7f8fa;

  .search-panel {
    background-color: #FFFFFF;
    padding: 20px 7px 7px 7px;

    .search-box-wrapper {
      padding: 0;

      .search-box {
        display: flex;
        height: 40px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        gap: 0;

        .search-bar {
          flex: 1;
          padding: 0;
          background-color: #FFFFFF;

          :deep(.van-search__content) {
            border-radius: 15px;
            background-color: #f7f8fa;
          }
        }

        .search-btn {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 36px;
          width: 60px;
          margin-left: 5px;
          background-color: #1989fa;
          color: white;
          border-radius: 0 10px 10px 0;
          font-size: 14px;
          padding: 0;
        }
      }
    }
  }

  .todo-list {
    padding: 12px;
  }

  .todo-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .todo-content {
      padding: 16px;

      .todo-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 8px;
      }

      .todo-desc {
        font-size: 14px;
        color: #646566;
        margin-bottom: 12px;
      }

      .todo-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;

        .todo-time {
          color: #969799;
        }

        .todo-status {
          padding: 2px 6px;
          border-radius: 4px;
          
          &.pending {
            background-color: #1989fa;
            color: #fff;
          }

          &.done {
            background-color: #07c160;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>