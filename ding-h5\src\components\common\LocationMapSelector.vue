<template>
  <div class="location-map-selector" v-show="visible">
    <div class="map-header">
      <div class="title">选择位置</div>
      <div class="close-btn" @click="handleClose">关闭</div>
    </div>
    <div id="location-map" class="map-container"></div>

    <!-- 固定在地图中心的标记图标，放在地图容器外部以避免被覆盖 -->
    <div class="center-marker-container">
      <img src="/custom_location.png" class="center-marker-icon" alt="位置标记" />
    </div>

    <!-- 背景遮罩 -->
    <div class="detail-mask" v-show="isShowDetailBox" @click="handleCloseDetailBox"></div>

    <!-- 详情框 -->
    <div class="detail-box" v-show="isShowDetailBox">
      <div class="detail-header">
        <h3>{{ currentLicense?.companyName || '零售户信息' }}</h3>
        <div class="close-icon" @click="handleCloseDetailBox">关闭</div>
      </div>
      <div class="detail-content">
        <div class="detail-info">
          <!-- 照片区域 -->
          <div class="left-image">
            <img v-if="currentLicense?.lastCenterPoho && currentLicense.lastCenterPoho.length > 0"
                 :src="currentLicense.lastCenterPoho[0].filthPath" class="store-image" alt="门店照片" />
            <img v-else src="/picture-icon.svg" class="store-image" alt="默认门店照片" />
          </div>

          <!-- 信息区域 -->
          <div class="right-info">
            <div class="info-item">
              <span class="label">地址：</span>
              <span class="value">{{ currentLicense?.businessAddr || '暂无数据' }}</span>
            </div>
            <div class="info-item">
              <span class="label">许可证号：</span>
              <span class="value">{{ currentLicense?.licNo || '暂无数据' }}</span>
            </div>
            <div class="info-item" v-if="currentLicense?.distance">
              <span class="label">距您</span>
              <span class="value distance">{{ currentLicense.distance || '--' }}米</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加定位按钮 -->
    <div class="position-box" :class="{ 'position-box-up': isShowDetailBox }" @click="handleLocationClick">
      <img src="@/assets/position.svg" class="position-icon" alt="定位" />
    </div>

    <div class="map-footer">
      <div class="tip-text">拖动地图，将中心点对准目标位置</div>
      <div class="button-group">
        <div class="confirm-btn" @click="handleConfirm">确认选择</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as dd from 'dingtalk-jsapi';
import { showSuccessToast, showFailToast, showConfirmDialog, Icon as VanIcon } from 'vant';
import { gcj02ToWgs84, wgs84ToGcj02 } from '@/utils/coordinate';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  originalLat: {
    type: Number,
    default: null
  },
  originalLon: {
    type: Number,
    default: null
  },
  licenseInfo: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'confirm', 'update:visible']);

// 地图相关变量
const map = ref(null);
const locationMarker = ref(null); // 当前位置标记
const originalMarker = ref(null); // 零售户旧地址标记
const currentLat = ref(null);
const currentLon = ref(null);

// 地图中心点位置（即选中的位置）
const centerLat = ref(null);
const centerLon = ref(null);
const centerGcjLat = ref(null);
const centerGcjLon = ref(null);

// 弹窗相关变量
const isShowDetailBox = ref(false);
const currentLicense = ref(null);

// 监听visible变化，当显示地图时初始化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    console.log('地图选择器显示，准备初始化地图');
    console.log('当前原始位置坐标:', props.originalLat, props.originalLon);

    // 每次显示时，重置地图对象，强制重新初始化
    if (map.value) {
      console.log('重置地图对象，强制重新初始化');

      // 清理资源
      try {
        // 移除所有事件监听器
        map.value.removeEventListener('moveend', handleMapMoveEnd);
        map.value.removeEventListener('zoomend');
        map.value.removeEventListener('dragend');
        map.value.removeEventListener('load');

        // 移除标记
        if (locationMarker.value) {
          map.value.removeOverLay(locationMarker.value);
          locationMarker.value = null;
        }

        // 移除零售户旧地址标记
        if (originalMarker.value) {
          map.value.removeOverLay(originalMarker.value);
          originalMarker.value = null;
        }

        // 重置地图对象
        map.value = null;

        console.log('地图资源清理完成');
      } catch (error) {
        console.error('清理地图资源时出错:', error);
      }
    }

    // 延迟一下初始化，确保DOM已经渲染
    setTimeout(() => {
      // 确保地图容器存在
      const mapContainer = document.getElementById('location-map');
      if (mapContainer) {
        console.log('地图容器已找到，尺寸:', mapContainer.clientWidth, 'x', mapContainer.clientHeight);
        initMap();
      } else {
        console.error('找不到地图容器元素');
      }
    }, 300); // 增加延迟时间
  } else {
    // 地图隐藏时，可能需要清理资源
    if (map.value) {
      console.log('地图选择器隐藏，清理地图资源');
      map.value.removeEventListener('moveend', handleMapMoveEnd);
    }
  }
});

// 组件挂载后预加载天地图SDK
onMounted(() => {
  console.log('组件挂载，预加载天地图SDK');
  // 预加载天地图SDK，但不初始化地图
  loadTianDiTuSDK().catch(err => {
    console.error('预加载天地图SDK失败:', err);
  });
});

// 初始化地图
const initMap = async () => {
  try {
    console.log('开始初始化地图');

    // 如果地图已经初始化，则不重复初始化
    if (map.value) {
      console.log('地图已初始化，跳过');
      return;
    }

    // 确保天地图SDK已加载
    if (!window.T) {
      console.log('加载天地图SDK');
      await loadTianDiTuSDK();
    }

    // 获取当前位置
    console.log('获取当前位置');
    await getCurrentLocation();

    console.log('当前位置:', currentLon.value, currentLat.value);

    // 确保DOM元素存在
    const mapContainer = document.getElementById('location-map');
    if (!mapContainer) {
      console.error('找不到地图容器元素');
      return;
    }

    console.log('初始化地图对象');
    // 初始化地图
    map.value = new T.Map('location-map', {
      zoom: 17,
      center: new T.LngLat(currentLon.value, currentLat.value),
      projection: 'EPSG:4326'
    });

    console.log('地图对象创建成功');

    try {
      // 直接初始化地图功能，不等待load事件
      console.log('直接初始化地图功能');

      // 延迟一下，确保地图已经渲染
      setTimeout(() => {
        try {
          console.log('开始添加标记和事件监听');

          // 添加当前位置标记
          addLocationMarker();
          console.log('当前位置标记已添加');

          // 添加零售户旧地址标记（如果有）
          addOriginalMarker();
          console.log('零售户旧地址标记已添加');

          // 初始化中心点位置
          updateCenterPosition();
          console.log('中心点位置已更新');

          // 添加地图移动结束事件
          map.value.addEventListener('moveend', handleMapMoveEnd);

          // 添加地图缩放结束事件
          map.value.addEventListener('zoomend', () => {
            console.log('地图缩放结束');
            updateCenterPosition();
          });

          // 添加地图拖动结束事件
          map.value.addEventListener('dragend', () => {
            console.log('地图拖动结束');
            updateCenterPosition();
          });

          console.log('所有初始化步骤完成');
        } catch (innerError) {
          console.error('延迟初始化地图功能时出错:', innerError);
        }
      }, 300);

      // 也监听load事件，以防万一
      map.value.addEventListener('load', () => {
        console.log('地图load事件触发');
        updateCenterPosition();
      });
    } catch (error) {
      console.error('初始化地图功能时出错:', error);
    }

    // 添加错误处理
    window.onerror = function(message, error) {
      console.error('地图加载错误:', message, error);
    };
  } catch (error) {
    console.error('初始化地图时出错:', error);
  }
};

// 地图移动结束事件处理
const handleMapMoveEnd = () => {
  updateCenterPosition();
};

// 更新中心点位置
const updateCenterPosition = () => {
  if (!map.value) {
    console.error('地图对象不存在，无法更新中心点位置');
    return;
  }

  try {
    const center = map.value.getCenter();

    if (!center || typeof center.lng === 'undefined' || typeof center.lat === 'undefined') {
      console.error('获取地图中心点失败:', center);
      return;
    }

    centerLon.value = center.lng;
    centerLat.value = center.lat;

    // 转换为GCJ02坐标系（高德坐标系）
    try {
      const [gcjLng, gcjLat] = wgs84ToGcj02(center.lng, center.lat);
      centerGcjLon.value = gcjLng;
      centerGcjLat.value = gcjLat;
    } catch (error) {
      console.error('坐标转换失败:', error);
      // 如果转换失败，使用原始坐标
      centerGcjLon.value = center.lng;
      centerGcjLat.value = center.lat;
    }

    console.log('地图中心点位置更新:', {
      wgs84: { lng: centerLon.value, lat: centerLat.value },
      gcj02: { lng: centerGcjLon.value, lat: centerGcjLat.value }
    });
  } catch (error) {
    console.error('更新中心点位置时出错:', error);
  }
};

// 加载天地图SDK
const loadTianDiTuSDK = () => {
  return new Promise((resolve, reject) => {
    if (window.T) {
      console.log('天地图SDK已加载');
      resolve();
      return;
    }

    console.log('开始加载天地图SDK');

    // 移除可能存在的旧脚本
    const oldScript = document.querySelector('script[src*="tianditu"]');
    if (oldScript) {
      console.log('移除旧的天地图脚本');
      oldScript.parentNode.removeChild(oldScript);
    }

    const script = document.createElement('script');
    script.src = 'https://api.tianditu.gov.cn/api?v=4.0&tk=e7d5714afd6f840366a1e9b4e7675236';

    script.onload = () => {
      console.log('天地图SDK加载成功');
      // 确保全局对象T存在
      if (window.T) {
        resolve();
      } else {
        console.error('天地图SDK加载成功但全局对象T不存在');
        reject(new Error('天地图SDK加载成功但全局对象T不存在'));
      }
    };

    script.onerror = (err) => {
      console.error('天地图SDK加载失败:', err);
      reject(err);
    };

    document.head.appendChild(script);
    console.log('天地图SDK脚本已添加到页面');
  });
};

// 获取当前位置
const getCurrentLocation = async () => {
  try {
    const res = await dd.device.geolocation.get({
      targetAccuracy: 200,
      coordinate: 1,
      withReGeocode: true
    });

    // 转换坐标系
    const [wgsLng, wgsLat] = gcj02ToWgs84(res.longitude, res.latitude);

    currentLon.value = wgsLng;
    currentLat.value = wgsLat;

    return { longitude: wgsLng, latitude: wgsLat };
  } catch (error) {
    console.error('获取位置失败:', error);
    // 使用默认坐标（湛江市）
    currentLon.value = 110.387653;
    currentLat.value = 21.260024;
    return { longitude: 110.387653, latitude: 21.260024 };
  }
};

// 添加当前位置标记
const addLocationMarker = () => {
  if (!map.value || !currentLon.value || !currentLat.value) return;

  const position = new T.LngLat(currentLon.value, currentLat.value);

  // 移除旧的标记（如果存在）
  if (locationMarker.value) {
    map.value.removeOverLay(locationMarker.value);
  }

  // 创建新的标记，使用与map页面一致的样式
  locationMarker.value = new T.Marker(position, {
    icon: new T.Icon({
      iconUrl: '/location-marker.svg', // 确保使用与map页面相同的图标
      iconSize: new T.Point(32, 32),
      iconAnchor: new T.Point(16, 16)
    })
  });

  map.value.addOverLay(locationMarker.value);
};

// 添加零售户旧地址标记
const addOriginalMarker = () => {
  // 检查是否有原始位置坐标
  if (!map.value) {
    console.error('地图未初始化，无法添加标记');
    return;
  }

  if (!props.originalLat || !props.originalLon) {
    console.warn('原始位置坐标无效，无法添加标记:', props.originalLat, props.originalLon);
    return;
  }

  console.log('准备添加零售户旧地址标记，原始坐标 (GCJ-02):', props.originalLon, props.originalLat);

  try {
    // 注意：props.originalLat 和 props.originalLon 是高德坐标系（GCJ-02）
    // 需要转换为WGS84坐标系，因为天地图使用的是WGS84坐标系
    const [wgsLon, wgsLat] = gcj02ToWgs84(props.originalLon, props.originalLat);
    console.log('转换后的坐标 (WGS84):', wgsLon, wgsLat);

    // 创建位置对象（使用WGS84坐标系）
    const position = new T.LngLat(wgsLon, wgsLat);

    // 移除旧的标记（如果存在）
    if (originalMarker.value) {
      console.log('移除旧的零售户标记');
      map.value.removeOverLay(originalMarker.value);
      originalMarker.value = null;
    }

    // 创建新的标记
    originalMarker.value = new T.Marker(position, {
      icon: new T.Icon({
        iconUrl: '/green.png', // 使用green.png作为零售户旧地址图标
        iconSize: new T.Point(32, 32),
        iconAnchor: new T.Point(16, 16)
      })
    });

    // 添加标记到地图
    map.value.addOverLay(originalMarker.value);

    // 添加点击事件
    originalMarker.value.addEventListener('click', () => handleOriginalMarkerClick());

    console.log('零售户旧地址标记添加成功');

    // 移动地图到标记点位置
    map.value.panTo(position);
    console.log('地图已移动到标记点位置');
  } catch (error) {
    console.error('添加零售户旧地址标记失败:', error);
  }
};

// 处理零售户旧地址标记点击
const handleOriginalMarkerClick = () => {
  console.log('零售户旧地址标记点击');
  console.log(props.licenseInfo)

  // 创建零售户信息对象
  // 如果没有传入许可证信息，创建一个默认对象
  currentLicense.value = props.licenseInfo ? { ...props.licenseInfo } : {
    companyName: '零售户旧地址',
    businessAddr: '原始地址',
    licNo: '暂无许可证号',
    latitude: props.originalLat,
    longitude: props.originalLon
  };

  // 确保坐标值是数字类型
  if (currentLicense.value.latitude) {
    currentLicense.value.latitude = Number(currentLicense.value.latitude);
  } else if (props.originalLat) {
    currentLicense.value.latitude = Number(props.originalLat);
  }

  if (currentLicense.value.longitude) {
    currentLicense.value.longitude = Number(currentLicense.value.longitude);
  } else if (props.originalLon) {
    currentLicense.value.longitude = Number(props.originalLon);
  }

  // 计算距离
  if (currentLat.value && currentLon.value && props.originalLat && props.originalLon) {
    const distance = calculateDistance(
      currentLat.value,
      currentLon.value,
      props.originalLat,
      props.originalLon
    );

    // 添加距离信息
    currentLicense.value.distance = Math.round(distance);
  }

  console.log('处理后的零售户信息:', currentLicense.value);

  // 显示详情框
  isShowDetailBox.value = true;
};

// 移动地图到当前位置
const moveToCurrentLocation = () => {
  if (!map.value || !currentLon.value || !currentLat.value) return;

  const position = new T.LngLat(currentLon.value, currentLat.value);
  map.value.panTo(position);

  // 更新中心点位置
  updateCenterPosition();
};

// 处理定位按钮点击
const handleLocationClick = async () => {
  try {
    // 显示加载提示
    showSuccessToast('正在定位...');

    // 重新获取当前位置
    await getCurrentLocation();

    // 更新当前位置标记
    addLocationMarker();

    // 移动地图到当前位置
    moveToCurrentLocation();
  } catch (error) {
    console.error('定位失败:', error);
    showFailToast('定位失败');
  }
};

// 计算两点之间的距离（米）
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371000; // 地球半径，单位米
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// 处理关闭按钮点击
const handleClose = () => {
  // 关闭详情框
  isShowDetailBox.value = false;
  // 关闭地图选择器
  emit('update:visible', false);
  emit('close');
};

// 处理关闭详情框
const handleCloseDetailBox = () => {
  isShowDetailBox.value = false;
  currentLicense.value = null;
};

// 处理确认按钮点击
const handleConfirm = async () => {
  console.log('确认按钮点击，当前中心点位置:', {
    centerLat: centerLat.value,
    centerLon: centerLon.value,
    centerGcjLat: centerGcjLat.value,
    centerGcjLon: centerGcjLon.value
  });

  // 强制更新一次中心点位置
  if (map.value) {
    updateCenterPosition();
  }

  // 再次检查中心点位置
  if (!centerLat.value || !centerLon.value) {
    console.error('中心点位置获取失败，尝试从地图对象直接获取');

    // 尝试直接从地图对象获取中心点
    if (map.value) {
      try {
        const center = map.value.getCenter();
        if (center && center.lng && center.lat) {
          centerLon.value = center.lng;
          centerLat.value = center.lat;

          // 转换为GCJ02坐标系
          const [gcjLng, gcjLat] = wgs84ToGcj02(center.lng, center.lat);
          centerGcjLon.value = gcjLng;
          centerGcjLat.value = gcjLat;

          console.log('直接从地图获取中心点成功:', {
            centerLat: centerLat.value,
            centerLon: centerLon.value
          });
        } else {
          console.error('地图中心点获取失败');
          showFailToast('无法获取位置信息');
          return;
        }
      } catch (error) {
        console.error('获取地图中心点出错:', error);
        showFailToast('无法获取位置信息');
        return;
      }
    } else {
      showFailToast('地图未初始化，无法获取位置信息');
      return;
    }
  }

  // 如果有原始位置，检查距离
  if (props.originalLat && props.originalLon) {
    // 注意：props.originalLat 和 props.originalLon 是高德坐标系（GCJ-02）
    // centerLat.value 和 centerLon.value 是WGS84坐标系
    // 为了准确计算距离，需要在同一坐标系下进行计算

    // 方法1：将中心点坐标转换为高德坐标系，然后计算距离
    const distance = calculateDistance(
      props.originalLat,
      props.originalLon,
      centerGcjLat.value, // 使用已转换为GCJ-02的中心点坐标
      centerGcjLon.value
    );

    console.log('选择位置与原始位置距离（高德坐标系）：', distance, '米');
    console.log('原始位置（高德坐标系）:', props.originalLon, props.originalLat);
    console.log('选择位置（高德坐标系）:', centerGcjLon.value, centerGcjLat.value);

    // 如果距离超过50米，显示确认弹窗
    if (distance > 50) {
      try {
        await showConfirmDialog({
          title: '位置变化提醒',
          message: `选择的位置与原始位置相比变化了${Math.round(distance)}米，是否确认选择？`,
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        });
        // 用户确认，继续提交
      } catch (error) {
        // 用户取消，不提交
        return;
      }
    }
  }

  // 发送确认事件，传递选中的位置
  const locationData = {
    latitude: centerGcjLat.value,
    longitude: centerGcjLon.value,
    wgsLatitude: centerLat.value,
    wgsLongitude: centerLon.value
  };

  console.log('发送位置数据:', locationData);
  emit('confirm', locationData);

  // 关闭地图
  emit('update:visible', false);
};

// 组件卸载时清理
onUnmounted(() => {
  console.log('组件卸载，清理资源');
  if (map.value) {
    try {
      // 移除所有事件监听器
      map.value.removeEventListener('moveend', handleMapMoveEnd);
      map.value.removeEventListener('zoomend');
      map.value.removeEventListener('dragend');
      map.value.removeEventListener('load');

      // 移除标记
      if (locationMarker.value) {
        map.value.removeOverLay(locationMarker.value);
      }

      // 移除零售户旧地址标记
      if (originalMarker.value) {
        map.value.removeOverLay(originalMarker.value);
      }

      console.log('地图资源清理完成');
    } catch (error) {
      console.error('清理地图资源时出错:', error);
    }
  }
});
</script>

<style scoped>
.location-map-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 2000; /* 提高z-index，确保在其他元素之上 */
  display: flex;
  flex-direction: column;
  isolation: isolate; /* 创建新的层叠上下文，防止子元素影响其他页面 */
}

.map-header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 18px;
  font-weight: 500;
}

.close-btn {
  color: #1989fa;
  font-size: 16px;
}

.map-container {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
}

/* 中心标记容器 */
.center-marker-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none; /* 允许点击穿透到地图 */
  z-index: 9999; /* 确保在最上层 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 中心标记图标 - 调整位置使底部为中心点 */
.center-marker-icon {
  width: 36px; /* 图标宽度 */
  height: 36px; /* 图标高度 */
  transform: translateY(-18px); /* 向上偏移半个图标高度，使底部对准中心点 */
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)); /* 添加阴影增强可见性 */
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.map-footer {
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 15px;
  border-top: 1px solid #eee;
}

.tip-text {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.location-btn {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  color: #1989fa;
  border-radius: 50%;
  margin-right: 10px;
  border: 1px solid #1989fa;
  font-size: 20px;
}

.confirm-btn {
  flex: 1;
  max-width: 70%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #1989fa;
  color: #fff;
  border-radius: 20px;
  font-size: 16px;
}

/* 背景遮罩 */
.detail-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998; /* 确保在地图上方，但在详情框下方 */
}

/* 详情框样式 */
.detail-box {
  position: absolute;
  bottom: 95px;
  left: 10px;
  right: 10px;
  background: #fff;
  padding: 15px 15px 0;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3); /* 增强阴影效果 */
  z-index: 9999; /* 大幅提高z-index，确保在最上层 */
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.close-icon {
  color: #1989fa;
  font-size: 14px;
  cursor: pointer;
}

.detail-content {
  margin-bottom: 10px;
}

.detail-info {
  display: flex;
  position: relative;
}

.left-image {
  flex: 0 0 60px;
  height: 90px;
  margin-right: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.store-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.right-info {
  flex: 1;
}

.info-item {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 22px;
  padding-right: 30px;
}

.label {
  color: #666;
}

.value {
  color: #333;
}

.value.distance {
  color: #ff6600;
}

/* 定位按钮样式 */
.position-box {
  position: absolute;
  right: 15px;
  bottom: 100px;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 900;
  transition: bottom 0.3s;
}

.position-box-up {
  bottom: 200px; /* 当详情框显示时，定位按钮上移 */
}

.position-icon {
  width: 24px;
  height: 24px;
}
</style>
