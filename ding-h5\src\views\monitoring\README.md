# 考勤监控系统

## 功能概述

考勤监控系统是一个用于监控稽查人员工作情况的统计分析工具，提供了以下功能：

1. 按不同时间维度（日、周、月、季度、年）查看统计数据
2. 按中队和人员筛选查看数据
3. 展示系统使用情况的关键指标：
   - 系统登录人数
   - 检客户次数
   - 品规识别情况（识别次数、识别品规数）
   - 异常烟数
   - 正常烟数和异常烟数统计

## 页面结构

页面主要分为以下几个部分：

1. **筛选区域**：用于选择时间范围、中队和人员
2. **数据概览卡片**：展示关键指标的汇总数据和环比趋势
3. **图表区域**：包含三个图表，分别展示检客户统计、品规识别统计和正常/异常烟统计
4. **排名表格**：展示人员的绩效排名（仅在选择具体中队时显示）

## API接口说明

系统需要以下API接口支持：

### 1. 获取中队列表

```
GET /api/dingapp/monitoring/teams
```

**返回数据格式**：

```json
[
  { "id": 1, "name": "第一中队" },
  { "id": 2, "name": "第二中队" },
  ...
]
```

### 2. 获取人员列表

```
GET /api/dingapp/monitoring/personnel?teamId={teamId}
```

**参数**：
- `teamId`: 中队ID

**返回数据格式**：

```json
[
  { "id": 1, "name": "张三" },
  { "id": 2, "name": "李四" },
  ...
]
```

### 3. 获取监控概览数据

```
GET /api/dingapp/monitoring/data
```

**参数**：
- `timeRange`: 时间范围，可选值：day, week, month, quarter, year
- `teamId`: 中队ID，可选
- `personnelId`: 人员ID，可选

**返回数据格式**：

```json
{
  "loginCount": 56,
  "loginTrend": 5.2,
  "inspectionCount": 78,
  "inspectionTrend": -2.1,
  "recognitionCount": 25,
  "recognitionTrend": 8.7,
  "specCount": 2500,
  "specTrend": 3.4,
  "abnormalCigaretteCount": 32,
  "abnormalCigaretteTrend": 12.5
}
```

注意：
- `loginCount` 表示系统登录人数
- `specCount` 是 `recognitionCount` 的100倍，表示识别品规数是品规识别次数的100倍

### 4. 获取检客户统计数据

```
GET /api/dingapp/monitoring/inspection-data
```

**参数**：
- `timeRange`: 时间范围，可选值：day, week, month, quarter, year
- `teamId`: 中队ID，可选
- `personnelId`: 人员ID，可选

**返回数据格式**：

```json
{
  "xAxis": ["00:00", "03:00", "06:00", "09:00", "12:00", "15:00", "18:00", "21:00"],
  "series": [
    {
      "name": "检客户次数",
      "data": [120, 132, 101, 134, 90, 230, 210, 182]
    }
  ]
}
```

### 5. 获取品规识别统计数据

```
GET /api/dingapp/monitoring/recognition-data
```

**参数**：
- `timeRange`: 时间范围，可选值：day, week, month, quarter, year
- `teamId`: 中队ID，可选
- `personnelId`: 人员ID，可选

**返回数据格式**：

```json
{
  "xAxis": ["00:00", "03:00", "06:00", "09:00", "12:00", "15:00", "18:00", "21:00"],
  "series": [
    {
      "name": "品规识别次数",
      "data": [120, 132, 101, 134, 90, 230, 210, 182]
    },
    {
      "name": "识别品规数",
      "data": [220, 182, 191, 234, 290, 330, 310, 182]
    }
  ]
}
```

### 6. 获取正常/异常烟统计数据

```
GET /api/dingapp/monitoring/cigarette-data
```

**参数**：
- `timeRange`: 时间范围，可选值：day, week, month, quarter, year
- `teamId`: 中队ID，可选
- `personnelId`: 人员ID，可选

**返回数据格式**：

```json
{
  "xAxis": ["00:00", "03:00", "06:00", "09:00", "12:00", "15:00", "18:00", "21:00"],
  "series": [
    {
      "name": "正常烟数",
      "data": [320, 332, 301, 334, 390, 330, 320, 382]
    },
    {
      "name": "异常烟数",
      "data": [20, 32, 21, 34, 30, 30, 20, 32]
    }
  ]
}
```

### 7. 获取人员排名数据

```
GET /api/dingapp/monitoring/personnel-ranking
```

**参数**：
- `timeRange`: 时间范围，可选值：day, week, month, quarter, year
- `teamId`: 中队ID

**返回数据格式**：

```json
[
  { "rank": 1, "name": "张三", "loginCount": 12, "inspectionCount": 27, "recognitionCount": 8, "specCount": 800 },
  { "rank": 2, "name": "李四", "loginCount": 10, "inspectionCount": 23, "recognitionCount": 7, "specCount": 700 },
  ...
]
```

注意：
- `loginCount` 表示系统登录人数
- `specCount` 是 `recognitionCount` 的100倍，表示识别品规数是品规识别次数的100倍

## 开发说明

1. 本系统使用Vue 3 + Vant UI + ECharts开发
2. 图表组件位于 `src/components/charts/` 目录下
3. API接口定义位于 `src/api/monitoring.js` 文件中
4. 系统已实现优雅降级，当API接口不可用时会使用模拟数据展示
