<template>
  <div class="main-app">
    <van-tabs v-model:active="currentTab" @change="handleTabsChange">
      <van-tab v-for="(item, index) in items" :key="index" :dot="item.badge" :badge="item.text" :title="item.title" :show-zero-badge="false" >
        <template #title>
          <!-- <van-badge v-if="item.badge" :content="item.text" type="primary">{{ item.title }}</van-badge> -->
          <span>{{ item.title }}</span>
        </template>
      </van-tab>
    </van-tabs>

    <div class="content">
      <div v-if="currentPage === '许可证'" style="overflow: hidden;">
        <xkz
          :yhyt-data="licMapData.yhytLicenseVO"
          :is-view="isView"
          :file-path="licMapData.photoPathList?.length > 0 ? licMapData.photoPathList[0].filthPath : '/picture-icon.svg'"
        />
      </div>
      <div v-if="currentPage === '违法记录'">
        <wfjl :yhyt-id="licMapData.yhytLicenseVO?.id" />
      </div>
      <div v-if="currentPage === '举报投诉'">
        <jbts :lic-id="licMapData.id" />
      </div>
      <div v-if="currentPage === '品规识别'">
        <pgsb :is-view="isView" :lic-data="licData" />
      </div>
      <div v-if="currentPage === '近一年订单'">
        <order :cust-code="licMapData.yhytLicenseVO?.custCode || ''" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Tabs as VanTabs, Tab as VanTab, showFailToast } from 'vant'
import { http } from '@/utils/http'
import { useLicenseStore } from '@/stores/license'
import Xkz from '@/components/lshhx/xkz/index.vue'
import Wfjl from '@/components/lshhx/wfjl/index.vue'
import Jbts from '@/components/lshhx/jbts/index.vue'
import Pgsb from '@/components/lshhx/pgsb/index.vue'
import Order from '@/components/lshhx/order/index.vue'

const route = useRoute()
const currentTab = ref(0)
const currentPage = ref('许可证')
const isView = ref(false)
const licMapData = ref({})
const licData = ref({})

const items = ref([
  {
    title: '许可证',
    content: '',
    badge: false,
    text: ''
  },
  {
    title: '违法记录',
    content: 'desc',
    badge: false,
    text: 0
  },
  {
    title: '举报投诉',
    content: 'desc',
    badge: false,
    text: 0
  },
  {
    title: '品规识别',
    content: 'desc',
    badge: false,
    text: ''
  },
  {
    title: '近一年订单',
    content: 'desc',
    badge: false,
    text: ''
  }
])

const handleTabsChange = (index) => {
  currentPage.value = items.value[index].title
}

const updateBadgeCounts = async () => {
  try {
    const countRes = await http.get('/api/dingapp/illegalrecords/getTotalCount', {
      params: {
        yhytId: licMapData.value.yhytLicenseVO?.id,
        licNo: licData.value.licNo
      }
    })

    if (countRes?.data) {
      // items.value[1].badge = countRes.data.illegalRecordCount > 0
      items.value[1].text = countRes.data.illegalRecordCount
      // items.value[2].badge = countRes.data.reportComplaintCount > 0
      items.value[2].text = countRes.data.reportComplaintCount
      // items.value[3].badge = countRes.data.identCount > 0
      // items.value[3].text = countRes.data.identCount
      //console.log(items.value)
    }
  } catch (error) {
    console.error('获取数量统计失败：', error)
  }
}

onMounted(async () => {
  try {
    const view = route.query.view
    isView.value = view === 'true'

    const licenseStore = useLicenseStore() //survey和map和search传入
      licData.value = licenseStore.currentLicense
    console.log('licData:', licData.value)
    console.log('licData.yhytLicenseVO:', licData.value?.yhytLicenseVO)
    console.log('custCode:', licData.value?.yhytLicenseVO?.custCode)
    const res = await http.get('/api/dingapp/license/getDingMapLicense', {
      params: {
        licNo: licData.value.licNo,
        yhytId: licData.value.id
      }
    })

    if (res?.data) {
      licMapData.value = res.data
      console.log('licMapData:', licMapData.value)
      console.log('licMapData.yhytLicenseVO:', licMapData.value.yhytLicenseVO)
      console.log('licMapData custCode:', licMapData.value.yhytLicenseVO?.custCode)
      await updateBadgeCounts()
    }
  } catch (error) {
    console.error('获取许可证详情失败：', error)
    showFailToast('获取数据失败')
  }
})
</script>

<style scoped>
.main-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  overflow-y: auto;
}
</style>