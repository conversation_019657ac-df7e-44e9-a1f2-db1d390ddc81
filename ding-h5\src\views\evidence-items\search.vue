<template>
  <div class="evidence-search">
    <!-- 搜索区域 -->
    <div class="search-panel">
      <div class="search-box-wrapper">
        <div class="search-box">
          <van-search
            v-model="searchValue"
            placeholder="搜索涉案物品"
            class="search-bar"
            @search="handleSearch"
          >
            <template #right-icon>
              <van-icon name="scan" size="24" @click="onScan" />

            </template>
          </van-search>
          <div class="search-btn" @click="handleSearch">
            搜索
          </div>
        </div>

        <div class="type-selector" @click="showFormatSelector">
          <span class="type-label">类型</span>
          <div class="type-value">
            <span>{{ selectedType.text }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- 物品列表 -->
    <div class="items-list">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getData"
      >
        <!-- <van-empty v-if="items.length === 0" description="暂无物品" /> -->
        <div class="cell-list">
          <van-cell-group >
            <van-cell
              v-for="item in items"
              :key="item.id"
              :title="item.productName"
              :value="`${Number(item.price).toFixed(2)}${item.priceUnit}`"
              @click="selectItem(item)"
              :class="{ 'selected-item': isItemSelected(item) }"
            >
              <template #right-icon>
                <div class="check-area">
                  <van-icon
                    v-if="isItemSelected(item)"
                    name="success"
                    class="selected-icon"
                    color="#1989fa"
                  />
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </van-list>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <van-button type="primary" block @click="goBack">返回统计页面 (已选 {{ evidenceStore.items.length }} 项)</van-button>
    </div>

    <!-- 类型选择弹窗 -->
    <van-popup v-model:show="showTypePopup" position="bottom" round>
      <van-picker
        :columns="typeOptions"
        @confirm="onTypeConfirm"
        @cancel="showTypePopup = false"
        show-toolbar
        title="选择类型"
      />
    </van-popup>

    <!-- 数量选择弹窗 -->
    <van-dialog
      v-model:show="showQuantityDialog"
      title="新增数量"
      show-cancel-button
      @confirm="confirmQuantity"
      @cancel="cancelQuantity"
    >
      <div class="quantity-dialog-content">
        <div class="item-header">
          <div class="item-name">{{ currentItem?.productName }}</div>
          <div class="item-price">{{ `${Number(currentItem?.price).toFixed(2)}${currentItem?.priceUnit}` }}</div>
        </div>
        <div class="quantity-controls">
          <div class="quantity-row">
              <span class="quantity-label">当前数量：</span>
              <span class="quantity-value">{{ currentItem?.quantity }}</span>
            </div>
          <div class="quantity-row">
            <span class="quantity-label">新增数量：</span>
            <van-stepper v-model="selectedQuantity" :decimal-length="1" min="0.1" />
          </div>
        </div>
        <!-- <div class="delete-button-container">
          <van-button type="danger" size="small" @click="handleDeleteItem">删除此物品</van-button>
        </div> -->
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useEvidenceItemsStore } from '@/stores/evidence-items'
import { http } from '@/utils/http'
import * as dd from 'dingtalk-jsapi'


const router = useRouter()
const evidenceStore = useEvidenceItemsStore()

// 搜索相关
const searchValue = ref('')
const selectedType = ref({ text: '全国在销卷烟和雪茄烟价格目录（附件1）', value: '品规' })
const showTypePopup = ref(false)

// 数量选择弹窗相关
const showQuantityDialog = ref(false)
const currentItem = ref(null)
const selectedQuantity = ref(1) // 默认数量为1

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 防抖处理的搜索函数
const debouncedSearch = debounce(() => {
  handleSearch()
}, 500) // 500毫秒的防抖时间

// 监听搜索值变化
watch(searchValue, () => {
  debouncedSearch()
})

// 在组件挂载时加载数据
onMounted(() => {
  // getData()
})


// 类型选项
const typeOptions = [
  { text: '全国在销卷烟和雪茄烟价格目录（附件1）', value: '品规' },
  { text: '广东在销卷烟和雪茄烟品牌价格目录（附件2）', value: '品牌' },
  { text: '广东在销卷烟和雪茄烟分类别价格目录（附件3）', value: '类别' },
  { text: '卷烟辅料参考价格表（附件4）', value: '品名' },
  // { text: '烟机', value: 'other' },
  // { text: '其他（非专卖品）', value: 'other' },

]

// 分页相关
const page = ref(1)
const pageSize = ref(20)
const loading = ref(false)
const finished = ref(false)

// 物品列表数据
const items = ref([])



// 获取数据
const getData = async () => {

  loading.value = true

  try {
    const res = await http.get(`/api/dingapp/priceStandards/list`, {
      params: {
        stdType: selectedType.value.value,
        productName: searchValue.value,
        current: page.value,
        size: pageSize.value,
      }
    })

    const newItems = res.data?.records || []
    items.value = page.value === 1 ? newItems : [...items.value, ...newItems]

    const total = res.data?.total || 0
    finished.value = items.value.length >= total

    if (!finished.value) {
      page.value++
    }
  } catch (error) {
    showToast('加载失败，请重试')
    finished.value = true
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  items.value = []
  page.value = 1
  finished.value = false
  getData()
}

// 显示类型选择器
const showFormatSelector = () => {
  showTypePopup.value = true
}

// 确认类型选择
const onTypeConfirm = (selectedOption) => {
  selectedType.value = selectedOption.selectedOptions[0]
  showTypePopup.value = false
  handleSearch()
}

// 选择物品
const selectItem = (item) => {
  // 如果是"类别"类型，直接添加新物品，不检查是否已选择
  if (['类别', '品牌', '品名'].includes(item.stdType)) {
    // 根据品规类型设置来源
    let priceSource = '';
    // 根据 selectedType.value 判断来源
    if (item.stdType == '品规') {
      priceSource = '附件1';
    } else if (item.stdType == '品牌') {
      priceSource = '附件2';
    } else if (item.stdType == '类别') {
      priceSource = '附件3';
    } else if (item.stdType == '品名') {
      priceSource = '附件4';
    }
    // 创建一个新对象，包含原始 item 的所有属性，并添加 price_source 字段
    const itemWithSource = {
      ...item,
      priceSource,
      quantity: 1, // 默认数量为1
      // 使用后端返回的evidenceType，如果没有则根据stdType设置默认值
      evidenceType: item.evidenceType || (item.stdType === '品规' ? '假烟' : '')
    };

    // 直接添加到 store，不累加
    evidenceStore.addItem(itemWithSource);
    showToast('已添加到选择列表');
    goBack();
    return;
  }

  // 对于非"类别"类型，保持原有逻辑
  if (!isItemSelected(item)) {
    // 根据品规类型设置来源
    let priceSource = '';
    // 根据 selectedType.value 判断来源
    if (item.stdType == '品规') {
      priceSource = '附件1';
    } else if (item.stdType == '品牌') {
      priceSource = '附件2';
    } else if (item.stdType == '类别') {
      priceSource = '附件3';
    } else if (item.stdType == '品名') {
      priceSource = '附件4';
    }

    // 创建一个新对象，包含原始 item 的所有属性，并添加 price_source 字段
    const itemWithSource = {
      ...item,
      priceSource,
      quantity: 1, // 默认数量为1
      // 使用后端返回的evidenceType，如果没有则根据stdType设置默认值
      evidenceType: item.evidenceType || (item.stdType === '品规' ? '假烟' : '')
    };

    // 添加到 store
    evidenceStore.addItem(itemWithSource);
    showToast('已添加到选择列表');
    goBack();
  } else {
    // 已选择的物品，显示数量选择弹窗
    const index = evidenceStore.items.findIndex(i => i.id === item.id);
    if (index !== -1) {
      const selectedItem = evidenceStore.items[index];
      currentItem.value = selectedItem;
      selectedQuantity.value = 1; // 默认新增数量为1，这个数量将会与原有数量相加
      showQuantityDialog.value = true;
    }
  }
}

// 确认数量选择
const confirmQuantity = () => {

  if (!currentItem.value) return;

  // 查找物品在store中的索引
  const index = evidenceStore.items.findIndex(i => i.id === currentItem.value.id);


  if (index !== -1) {
    // 计算新的总数量（原有数量 + 新增数量）
    const originalQuantity = parseFloat(currentItem.value.quantity) || 0;
    const additionalQuantity = parseFloat(selectedQuantity.value) || 0;
    const newTotalQuantity = (originalQuantity * 10 + additionalQuantity * 10) / 10; // 避免浮点数精度问题

    // 更新物品数量
    const updatedItem = {
      ...currentItem.value,
      quantity: newTotalQuantity // 更新为原有数量加上新增数量
    };



    // 从store中移除原物品
    evidenceStore.removeItem(index);

    // 将更新后的物品添加到数组开头（置顶）
    evidenceStore.addItem(updatedItem);

    showToast(`已添加${additionalQuantity}，总数量为${newTotalQuantity}`);
  }

  // 重置状态
  goBack()
  currentItem.value = null;
}

// 取消数量选择
const cancelQuantity = () => {
  // 重置状态
  currentItem.value = null;
}

// 删除当前物品
const handleDeleteItem = () => {
  if (!currentItem.value) return;

  // 查找物品在store中的索引
  const index = evidenceStore.items.findIndex(i => i.id === currentItem.value.id);

  if (index !== -1) {
    // 从store中删除物品
    evidenceStore.removeItem(index);
    showToast('已从选择列表移除');

    // 关闭弹窗
    showQuantityDialog.value = false;

    // 重置状态
    currentItem.value = null;
  }
}

// 检查物品是否已选择
const isItemSelected = (item) => {
  return evidenceStore.items.some(i => i.id == item.id)
}

// 返回统计页面
const goBack = () => {
  router.back()
}

// 扫码处理
const onScan = () => {
  dd.scan({
        type: 'bar',
        onSuccess: (res) => {
            const text = res.text
            if (!text) {
                showToast('未识别到条形码')
                return
            }
            searchValue.value = text
            page.value = 1;
            getData()
        },
        fail: (err) => {
            console.error('扫码失败:', err)
            showToast('扫码失败')
        }
    })
}
</script>

<style lang="scss" scoped>
.evidence-search {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f8fa;
}

.search-panel {
    background-color: #FFFFFF;
    padding: 20px 7px 7px 7px;

    .search-box-wrapper {
      padding: 0;

      .search-box {
        display: flex;
        height: 40px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        gap: 0;

        .search-bar {
          flex: 1;
          padding: 0;
          background-color: #FFFFFF;

          :deep(.van-search__content) {
            border-radius: 15px;
            background-color: #f7f8fa;
          }
        }

        .search-btn {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 36px;
          width: 60px;
          margin-left: 5px;
          background-color: #1989fa;
          color: white;
          border-radius: 0 10px 10px 0;
          font-size: 14px;
          padding: 0;
        }
      }

      .type-selector {
        margin-top: 4px;
        padding: 0 12px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #f7f8fa;
        border-radius: 8px;

        .type-label {
          font-size: 14px;
          color: #646566;
        }

        .type-value {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          color: #1989fa;
          font-weight: 500;

          .arrow-icon {
            color: #969799;
          }
        }

        &:active {
          background-color: #e8e8e8;
        }
      }
    }

    .add-btn-wrapper {
      margin-top: 12px;
      padding: 0 8px;

      .add-btn {
        border-radius: 18px;
        height: 36px;
      }
    }
  }

.items-list {
  flex: 1;
  overflow-y: auto;
  // padding: 12px;
}

.cell-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bottom-bar {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.items-list {
  flex: 1;
  overflow-y: auto;

  :deep(.selected-item) {
    background-color: #f0f9ff;

    .van-cell__title {
      color: #1989fa;
    }

    .van-cell__value {
      color: #1989fa;
    }
  }

  :deep(.check-area) {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.selected-icon) {
    font-size: 20px;
  }
}

/* 数量选择弹窗样式 */
.quantity-dialog-content {
  padding: 16px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.item-name {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  flex: 1;
}

.item-price {
  font-size: 14px;
  color: #666;
}

.quantity-controls {
  margin-top: 36px;
}

.quantity-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.quantity-row:last-child {
  margin-bottom: 0;
}

.quantity-label {
  font-size: 14px;
  color: #646566;
  width: 80px;
}

.quantity-value {
  font-size: 14px;
  color: #646566;

}

.delete-button-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>
