<template>
  <div class="survey-container">
    <div class="lsh-info-card">
      <h3 class="title blue">{{ companyName }}</h3>
      <div class="info-row">
        <span class="label">许可证号：</span>
        <span class="value">{{ licNo }}</span>
      </div>
      <div class="info-row">
        <span class="label">详细地址：</span>
        <span class="value">{{ address }}</span>
      </div>
      <img class="images" :src="filePath" @click="previewPic" />
    </div>

    <div class="photo-card">
      <div class="photo-header">
        <van-checkbox v-model="needPhoto" class="blue">
          <span class="blue">是否需要更新门面照片</span>
        </van-checkbox>
      </div>
      <p class="photo-tip">请拍摄3张门面照片，按照左、中、右顺序上传</p>
      <div class="photo-container" :class="{ 'disabled': !needPhoto }">
        <div class="photo-item">
          <h4>左侧照片</h4>
          <van-uploader
            v-model="fileListOne"
            :max-count="1"
            @delete="handleRemoveOne"
            :after-read="afterRead"
            :disabled="!needPhoto"
            :upload-text="!needPhoto?'仅允许查看':''"
          />
        </div>
        <div class="photo-item">
          <h4>正面照片</h4>
          <van-uploader
            v-model="fileListTwo"
            :max-count="1"
            @delete="handleRemoveTwo"
            :after-read="afterRead"
            :disabled="!needPhoto"
            :upload-text="!needPhoto?'仅允许查看':''"
          />
        </div>
        <div class="photo-item">
          <h4>右侧照片</h4>
          <van-uploader
            v-model="fileListThree"
            :max-count="1"
            @delete="handleRemoveThree"
            :after-read="afterRead"
            :disabled="!needPhoto"
            :upload-text="!needPhoto?'仅允许查看':''"
          />
        </div>
      </div>
    </div>

    <div class="survey-btn" v-if="isUnlicensed">
      <van-button
        type="primary"
        
        block
        @click="handleSubmitClick"
      >确定</van-button>
    </div>
    <div class="survey-btn" v-else >
      <van-button
        type="primary"
        block
        @click="handleNextClick"
      >下一步</van-button>
    </div>
    

  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { http } from '@/utils/http'
import { showSuccessToast, showFailToast,showToast, showImagePreview } from 'vant'
import * as dd from 'dingtalk-jsapi'
import { useRouter } from 'vue-router'
import { useLicenseStore } from '@/stores/license'

const route = useRoute()

const licNo = ref('')
const licId = ref('')
const companyName = ref('')
const filePath = ref('')
const address = ref('')
const needPhoto = ref(false)
const isUnlicensed = ref(false)

// 监听 needPhoto 变化，当取消勾选时清空图片
watch(needPhoto, (newVal) => {
  if (!newVal) {
    // 如果取消勾选，清空所有图片
    fileListOne.value = []
    fileListTwo.value = []
    fileListThree.value = []
  }
})
const toastShow = ref(false)
const explorationId = ref('')
const dingMapLicenseVoBasic = ref({})

const fileListOne = ref([])
const fileListTwo = ref([])
const fileListThree = ref([])



onMounted(async () => {
  if (route.query.xkzh) {
    licNo.value = route.query.xkzh
    await loadLicenseInfo()
  }
})


const loadLicenseInfo = async () => {
  try {
    const res = await http.get(`/api/dingapp/exploration/getTodayExploration?lincNo=${licNo.value}`)
    if (Object.keys(res.data).length !== 0) {
      const { dingMapLicenseVO, explorationVO } = res.data
      licNo.value = dingMapLicenseVO.licNo
      if(dingMapLicenseVO.licNo == ''){
        isUnlicensed.value = true;
      }
      companyName.value = dingMapLicenseVO.companyName
      address.value = dingMapLicenseVO.businessAddr
      filePath.value = dingMapLicenseVO.lastCenterPoho && dingMapLicenseVO.lastCenterPoho.length > 0 ? dingMapLicenseVO.lastCenterPoho[0].filthPath : ''
      explorationId.value = explorationVO.id
      licId.value = dingMapLicenseVO.id
      dingMapLicenseVoBasic.value = dingMapLicenseVO
      // 加载已有照片
      const resFileList = dingMapLicenseVO.photoPathList || []
      resFileList.forEach(file => {
        const fileItem = {
          uid: file.id,
          status: 'done',
          url: file.filthPath,
          fileName: file.fileName
        }

        if (fileItem.fileName.includes('left')) {
          fileListOne.value = [fileItem]
        } else if (fileItem.fileName.includes('center')) {
          fileListTwo.value = [fileItem]
        } else if (fileItem.fileName.includes('right')) {
          fileListThree.value = [fileItem]
        }
      })
    }
  } catch (error) {
    showFailToast('获取数据失败')
    console.error(error)
  }
}

const afterRead = async (file) => {
  console.log(file,'after')
  try {
    file.status = 'uploading'
    file.message = '上传中...'

    // 上传文件
    const formData = new FormData()
    formData.append('file', file.file)
    formData.append('objId', explorationId.value)
    formData.append('objName', 'exploration')

    // 根据上传组件判断添加不同的extName
    let extName = ''
    if (fileListOne.value.includes(file)) {
      extName = '_left'
    } else if (fileListTwo.value.includes(file)) {
      extName = '_center'
    } else if (fileListThree.value.includes(file)) {
      extName = '_right'
    }
    formData.append('extName', extName)

    const res = await http.post('/api/dingapp/file/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (res.data) {
      file.status = 'done'
      file.url = res.data.path
      file.uid = res.data.id
      // 上传位置信息
      await uploadLocation()
    } else {
      file.status = 'failed'
      file.message = '上传失败'
      showFailToast('上传失败')
    }
  } catch (error) {
    file.status = 'failed'
    file.message = '上传失败'
    showFailToast('上传失败')
    console.error(error)
  }
}

const handleRemoveOne = async (file) => {
  try {
    const res = await http.get('/api/dingapp/file/remove', {
      params: { ids: file.uid }
    })
    if (res.data) {
      fileListOne.value = []
      showSuccessToast('删除成功')
    } else {
      showFailToast('删除失败')
    }
  } catch (error) {
    showFailToast('删除失败')
    console.error(error)
  }
}

const handleRemoveTwo = async (file) => {
  try {
    const res = await http.get('/api/dingapp/file/remove', {
      params: { ids: file.uid }
    })
    if (res.data) {
      fileListTwo.value = []
      showSuccessToast('删除成功')
    } else {
      showFailToast('删除失败')
    }
  } catch (error) {
    showFailToast('删除失败')
    console.error(error)
  }
}

const handleRemoveThree = async (file) => {
  try {
    const res = await http.get('/api/dingapp/file/remove', {
      params: { ids: file.uid }
    })
    if (res.data) {
      fileListThree.value = []
      showSuccessToast('删除成功')
    } else {
      showFailToast('删除失败')
    }
  } catch (error) {
    showFailToast('删除失败')
    console.error(error)
  }
}

const uploadLocation = async () => {
  try {
    const res = await dd.device.geolocation.get({
        targetAccuracy: 200,
        coordinate: 1,
        withReGeocode: true
    })
    await http.post('/api/dingapp/exploration/submitExplorationCoordinate', {
      explorationId: explorationId.value,
      licenseId: licNo.value,
      longitude: res.longitude,
      latitude: res.latitude,
    })
  } catch (error) {
    console.error('上传位置信息失败:', error)
    showFailToast('上传位置信息失败')
  }
}



const router = useRouter()
const licenseStore = useLicenseStore()

const handleNextClick = async () => {
    if(needPhoto.value){
      if(fileListOne.value.length + fileListTwo.value.length + fileListThree.value.length !== 3){
        showToast("请先拍摄三张门面照片");
        return;
      }


      // 提交中间照片
      if (fileListTwo.value.length > 0 && needPhoto.value) {
        const res = await http.post('/api/dingapp/exploration/submitCenterPhoto', {
          explorationId: explorationId.value,
          licenseId: licId.value,
          fileId: fileListTwo.value[0].uid
        })
      }
    }


    // 将当前许可证信息存入store
    licenseStore.setCurrentLicense(dingMapLicenseVoBasic.value)
    // 设置explorationId到store中
    licenseStore.setExplorationId(explorationId.value)
    // 跳转到lshhx页面，设置view=false表示非查看模式
    router.push(`/lshhx?view=false`)
}


const handleSubmitClick = async () => {
    if(needPhoto.value){
      if(fileListOne.value.length + fileListTwo.value.length + fileListThree.value.length !== 3){
        showToast("请先拍摄三张门面照片");
        return;
      }

      // 提交中间照片
      if (fileListTwo.value.length > 0 && needPhoto.value) {
        const res = await http.post('/api/dingapp/exploration/submitCenterPhoto', {
          explorationId: explorationId.value,
          licenseId: licId.value,
          fileId: fileListTwo.value[0].uid
        })
      }
    }
    router.back();
}


const previewPic = () => {
    showImagePreview({
        images: [filePath.value],
        closeable: true
    })
}

</script>

<style lang="scss" scoped>
.survey-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .lsh-info-card {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 12px;
    }

    .info-row {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;

      .label {
        color: #666;
        min-width: 80px;
      }

      .value {
        color: #333;
        flex: 1;
      }
    }
  }

  .photo-card {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .photo-header {
      margin-bottom: 12px;
    }

    .photo-tip {
      color: #666;
      font-size: 14px;
      margin-bottom: 16px;
    }

    .photo-container {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      transition: opacity 0.3s;



      .photo-item {
        h4 {
          font-size: 14px;
          margin-bottom: 8px;
          color: #333;
        }
      }
    }
  }

  .survey-btn {
    padding: 16px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  }

  .blue {
    color: #1989fa;
  }
}
.images {
    width: 70%;
    height: 140px;
    border-radius: 5px;
    display: block;
    margin: 0 auto 15px;
    object-fit: contain;
}
</style>