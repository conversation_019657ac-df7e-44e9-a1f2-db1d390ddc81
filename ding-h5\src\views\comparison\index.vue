<template>
  <div class="comparison-container">
    <van-table
      :columns="columns"
      :data-source="dataSource"
      :bordered="true"
      :scroll="{ x: true }"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { showSuccessToast, showFailToast  } from 'vant'

const columns = ref([
  {
    title: ' ',
    dataIndex: 'item',
    key: 'name',
    width: 180
  },
  {
    title: '企业名称1',
    dataIndex: 'company1',
    key: 'name',
    width: 260
  },
  {
    title: '企业名称2',
    dataIndex: 'company2',
    key: 'age',
    width: 260
  }
])

const dataSource = ref([
  {
    item: '许可证号',
    company1: '',
    company2: ''
  },
  {
    item: '企业名称',
    company1: '',
    company2: ''
  },
  {
    item: '企业类型',
    company1: '',
    company2: ''
  }
])

onMounted(() => {
  try {
    const data = localStorage.getItem('comparisionData')
    if (data) {
      const parsedData = JSON.parse(data)
      dataSource.value = parsedData
    }
  } catch (error) {
    showFailToast('获取对比数据失败')
    console.error('获取对比数据失败:', error)
  }
})

onUnmounted(() => {
  localStorage.removeItem('comparisionData')
  console.log('对比数据已清除')
})
</script>

<style lang="scss" scoped>
.comparison-container {
  padding: 16px;
}
</style>