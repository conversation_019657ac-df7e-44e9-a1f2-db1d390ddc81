import { http } from '@/utils/http'

/**
 * 获取部门列表
 * 使用部门接口获取部门数据
 * @param {string} deptId 部门ID，用于筛选特定部门
 * @returns {Promise<Array>} 部门列表
 */
export function getDeptList(params) {
  return http.get('/api/blade-system/dept/select', { params })
}

/**
 * 获取部门树形结构
 * 注意：此接口会自动从当前登录用户中获取tenantId，无需手动传入
 * @returns {Promise<Array>} 部门树形结构
 */
export function getDeptTree() {
  return http.get('/api/blade-system/dept/tree')
}

/**
 * 获取懒加载部门树形结构
 * 注意：此接口会自动从当前登录用户中获取tenantId，无需手动传入
 * @param {number} parentId 父级部门ID
 * @returns {Promise<Array>} 部门树形结构
 */
export function getLazyDeptTree(parentId) {
  return http.get('/api/blade-system/dept/lazy-tree', { params: { parentId } })
}

/**
 * 根据父ID获取树形结构（包含子节点）
 * 注意：此接口会自动从当前登录用户中获取tenantId，无需手动传入
 * @param {number} parentId 父级部门ID
 * @returns {Promise<Array>} 包含子节点的部门树形结构
 */
export function getDeptTreeByParent(parentId) {
  return http.get('/api/blade-system/dept/tree-by-parent', { params: { parentId } })
}