import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import Vant from 'vant';
import 'vant/lib/index.css'
import { initDingTalk } from './utils/dingtalk'
import { initDebugMode } from './utils/debug'

// 初始化调试模式
initDebugMode();

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 注册 Vant 组件
app.use(Vant)

// 初始化钉钉环境
router.isReady().then(() => {
  initDingTalk().then(success => {
    if (success) {
      console.log('钉钉环境初始化成功');
    } else {
      console.error('钉钉环境初始化失败');
    }
  });
});

app.mount('#app')