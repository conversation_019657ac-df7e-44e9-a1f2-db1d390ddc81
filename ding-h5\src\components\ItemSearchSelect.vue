<template>
  <div class="search-select">
    <van-field
      v-model="selectedText"
      :label="label"
      :placeholder="placeholder"
      autosize
      type="textarea"
      readonly
      @click="showPopup = true"
    />
    <van-popup
      v-model:show="showPopup"
      position="bottom"
      round
      :style="{ height: '60%' }"
    >
      <div class="popup-container">
        <div class="popup-header">
          <van-search
            v-model="searchValue"
            :placeholder="searchPlaceholder"
            @search="onSearch"
          />
        </div>
        <div class="popup-content">
          <van-list
            :loading="loading"
            :finished="finished"
          >
            <van-cell
              v-for="item in filteredList"
              :key="item.value"
              :title="item.text"
              @click="onSelect(item)"
              clickable
            >
              <template #right-icon>
                <van-checkbox v-model="item.selected" />
              </template>
            </van-cell>
          </van-list>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { http } from '@/utils/http'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '点击选择'
  },
  searchPlaceholder: {
    type: String,
    default: '请输入搜索关键词'
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  selectedItems: { // 接收父组件传递的完整选中项
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

const showPopup = ref(false)
const searchValue = ref('')
const loading = ref(false)
const finished = ref(false)
const list = ref([])
const selectedMap = ref(new Map())

watch(showPopup, (newVal) => {
  if (newVal) {
    onLoad()
  } else {
    // 可选：在关闭弹窗时，根据 selectedMap 更新 modelValue
    const selectedValuesOnClose = Array.from(selectedMap.value.keys())
    emit('update:modelValue', selectedValuesOnClose)
    emit('select', Array.from(selectedMap.value.keys()).map(value => {
      const found = list.value.find(item => item.value === value)
      return found ? { value: found.value, text: found.text, selected: true } : { value: value, text: '未知', selected: true } // 处理不在当前列表中的已选项
    }))
  }
})

const selectedText = computed(() => {
  const selectedItems = list.value.filter(item => selectedMap.value.has(item.value))
  return selectedItems.length ? selectedItems.map(item => item.text).join(', ') : ''
})

const filteredList = computed(() => {
  if (!searchValue.value) return list.value
  return list.value.filter(item =>
    item.text.toLowerCase().includes(searchValue.value.toLowerCase())
  )
})

const onSearch = () => {
  list.value = []
  finished.value = false
  onLoad()
}

watch(searchValue, (newVal) => {
  list.value = []
  finished.value = false
  onLoad()
})



const onLoad = async () => {
  try {
    loading.value = true
    const res = await http.get(`/api/dingapp/productInfo/getSelectionList`, {
      params: {
        name: searchValue.value
      }
    })
    if (res.data) {
      finished.value = true
      // 保存当前选中项的信息
      const currentSelectedItems = Array.from(selectedMap.value.entries()).map(([value, _]) => {
        // 先从props.selectedItems中查找
        const selectedItem = props.selectedItems.find(item => item.value === value)
        if (selectedItem) {
          return { ...selectedItem, selected: true }
        }
        // 如果在props.selectedItems中没找到，再从list中查找
        const listItem = list.value.find(i => i.value === value)
        return listItem || { value, text: '未知', selected: true }
      })

      // 更新列表，同时保持已选中状态
      const newList = res.data.map(item => {
        // 检查是否在selectedItems中存在
        const selectedItem = props.selectedItems.find(si => si.value === item.id)
        return {
          value: item.id,
          text: selectedItem ? selectedItem.text : item.productName,
          selected: selectedMap.value.has(item.id)
        }
      })

      // 确保之前选中但不在新列表中的项也被保留
      const itemsNotInNewList = currentSelectedItems.filter(
        item => !newList.some(newItem => newItem.value === item.value)
      )

      list.value = [...newList, ...itemsNotInNewList]
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

watch(() => props.selectedItems, (newItems) => {
  // console.log('props.selectedItems changed:', newItems);
  if (newItems && newItems.length > 0) {
    selectedMap.value.clear();
    newItems.forEach(item => {
      selectedMap.value.set(item.value, true);
    });
    onLoad();
  }
});

const onSelect = (item) => {
  const isSelected = !selectedMap.value.has(item.value)
  if (isSelected) {
    selectedMap.value.set(item.value, true)
  } else {
    selectedMap.value.delete(item.value)
  }
  // 更新 list 中对应项的 selected 状态，以便 van-checkbox 显示正确
  const foundItemInList = list.value.find(listItem => listItem.value === item.value)
  if (foundItemInList) {
    foundItemInList.selected = isSelected
  }

  const selectedValues = Array.from(selectedMap.value.keys())
  const selectedItemsForEmit = Array.from(selectedMap.value.keys()).map(value => {
    const found = list.value.find(listItem => listItem.value === value)
    return found ? { value: found.value, text: found.text, selected: true } : { value: value, text: '未知', selected: true } // 处理不在当前列表中的已选项
  })

  emit('update:modelValue', selectedValues)
  emit('select', selectedItemsForEmit)
}
</script>

<style lang="scss" scoped>
.search-select {
  .popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .popup-header {
      padding: 8px;
    }

    .popup-content {
      flex: 1;
      overflow-y: auto;
    }
  }
}

.search-select .van-field__body {
  white-space: normal !important;
  word-break: break-all;
}

</style>