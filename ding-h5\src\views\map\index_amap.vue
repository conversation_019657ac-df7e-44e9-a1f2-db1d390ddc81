<template>
  <div class="map-container">
    <div id="map" class="map"></div>
    <div v-if="isShowDetailBox" class="detail-box">
      <div class="detail-header">
        <h3>{{ currentLicense.companyName }}</h3>
        <van-icon name="close" @click="handleCloseDetailBox" />
      </div>
      <div class="detail-content">
        <p>许可证号：{{ currentLicense.licenseNo }}</p>
        <p>地址：{{ currentLicense.businessAddr }}</p>
        <van-button type="primary" block @click="handleStartSurvey">开始勘查</van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'
import { http } from '@/utils/http'
import { showToast } from 'vant'

const router = useRouter()

// 地图相关数据
const currentLon = ref(null)
const currentLat = ref(null)
const currentAddress = ref('')
const mapLicenseList = ref([])
const map = ref(null)
const markers = ref([])

// 详情框相关数据
const isShowDetailBox = ref(false)
const currentLicense = ref(null)

// 初始化地图
const initMap = () => {
  map.value = new AMap.Map('map', {
    zoom: 15,
    center: [currentLon.value, currentLat.value],
    viewMode: '2D',
    resizeEnable: true,
    rotateEnable: false,
    pitchEnable: false
  })

  // 添加地图控件
  // map.value.addControl(new AMap.Scale())
  // map.value.addControl(new AMap.ToolBar({
  //   position: 'RB'
  // }))
  // map.value.addControl(new AMap.Geolocation({
  //   position: 'RB',
  //   offset: [10, 80]
  // }))
}

// 开始定位
const startLocation = async () => {
  try {
    // const res = await dd.device.geolocation.get({
    //   targetAccuracy: 200,
    //   coordinate: 1,
    //   withReGeocode: true
    // })
    
    const res = {
      longitude: 110.387653,
      latitude: 21.260024,
      address: '湛江市市场'
    }

    currentLon.value = res.longitude
    currentLat.value = res.latitude
    currentAddress.value = res.address

    if (map.value) {
      map.value.setCenter([currentLon.value, currentLat.value])
      loadMapLicenseList()
    }
  } catch (error) {
    // showToast.fail('获取位置信息失败')
    console.error('定位失败:', error)
  }
}

// 加载地图标记点
const loadMapLicenseList = async () => {
  try {
    const res = await http.get('/api/dingapp/license/getDingMapLicenseList', {
      params: {
        longitude: currentLon.value,
        latitude: currentLat.value
      }
    })
    console.log(res)
    if (res.data) {
      mapLicenseList.value = res.data
      updateMapMarkers()
    }
  } catch (error) {
    // showToast.fail('获取零售户列表失败')
    console.error('获取零售户列表失败:', error)
  }
}

// 更新地图标记点
const updateMapMarkers = () => {
  // 清除旧的标记点
  markers.value.forEach(marker => marker.remove())
  markers.value = []

  // 添加新的标记点
  mapLicenseList.value.forEach((license, index) => {
    const marker = new AMap.Marker({
      position: [license.longitude, license.latitude],
      icon: new AMap.Icon({
        size: new AMap.Size(32, 32),
        image: '/image/map.png',
        imageSize: new AMap.Size(32, 32)
      }),
      offset: new AMap.Pixel(-16, -32),
      title: license.companyName,
      clickable: true,
      animation: 'AMAP_ANIMATION_DROP',
      label: {
        content: license.companyName,
        direction: 'bottom',
        offset: new AMap.Pixel(0, 5)
      }
    })

    marker.on('click', () => handleMarkerClick(license))
    marker.setMap(map.value)
    markers.value.push(marker)
  })
}

// 处理标记点点击
const handleMarkerClick = (license) => {
  currentLicense.value = license
  isShowDetailBox.value = true
}

// 关闭详情框
const handleCloseDetailBox = () => {
  isShowDetailBox.value = false
  currentLicense.value = null
}

// 开始勘查
const handleStartSurvey = () => {
  if (currentLicense.value) {
    router.push(`/survey?xkzh=${currentLicense.value.licenseNo}&type=survey`)
  }
}

onMounted(() => {
  loadMapLicenseList()
  // 加载高德地图 SDK
  const script = document.createElement('script')
  script.src = 'https://webapi.amap.com/maps?v=2.0&key=c0cc52f53be04f6bf15b21f7ec14775b'
  script.onload = () => {
    initMap()
    startLocation()
  }
  document.head.appendChild(script)
})

onUnmounted(() => {
  if (map.value) {
    map.value.destroy()
  }
})
</script>

<style lang="scss" scoped>
.map-container {
  height: 100vh;
  position: relative;

  .map {
    height: 100%;
  }

  .detail-box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 16px;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h3 {
        margin: 0;
        font-size: 18px;
      }
    }

    .detail-content {
      p {
        margin: 8px 0;
        color: #666;
      }

      .van-button {
        margin-top: 16px;
      }
    }
  }
}
</style>