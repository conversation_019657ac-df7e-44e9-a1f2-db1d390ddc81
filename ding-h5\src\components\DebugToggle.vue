<template>
  <div
    v-if="isVisible"
    class="debug-toggle"
    @click="toggleDebug"
  >
    <van-icon name="bug-o" size="20" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { toggleVConsole, isDebugModeEnabled } from '@/utils/debug';
import { showToast } from 'vant';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const isVisible = ref(props.visible || import.meta.env.DEV);

// 切换调试模式
const toggleDebug = () => {
  const isEnabled = toggleVConsole();
  showToast(isEnabled ? '调试控制台已启用' : '调试控制台已禁用');
};

onMounted(() => {
  // 在开发环境或调试模式下显示
  isVisible.value = isVisible.value || isDebugModeEnabled();
});
</script>

<style scoped>
.debug-toggle {
  position: fixed;
  right: 10px;
  top: 10px;
  width: 36px;
  height: 36px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
</style>
