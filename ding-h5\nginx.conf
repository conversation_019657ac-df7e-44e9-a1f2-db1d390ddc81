server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html;

    # gzip配置
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # 前端路由配置
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理配置
    location /api/ {
        client_max_body_size 100M;
        if ($request_uri ~* /api/(.*)$) {
            set $new $1;
            proxy_pass http://**********:8080/$new;
        }
    }

    location /blade-auth/ {
        client_max_body_size 100M;
        if ($request_uri ~* /blade-auth/(.*)$) {
            set $new $1;
            proxy_pass http://**********:8080/blade-auth/$new;
        }
    }

    # 静态资源缓存配置（排除minio路径）
    location ^~ /(?!minio/).*\.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1d;
    }

    # Minio代理配置
    location /minio/ {
        client_max_body_size 100M;  # 允许上传最大100MB文件（按需调整）

        # 代理到 MinIO 后端，保持原始URI路径
        proxy_pass http://**********:9000/;
        rewrite ^/minio/(.*) /$1 break;

        # 确保代理头部正确传递
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Amz-Date $date_gmt;
        proxy_set_header Date $date_gmt;
        
        # 性能优化参数
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_connect_timeout 60s;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
}