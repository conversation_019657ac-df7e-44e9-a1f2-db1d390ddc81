<template>
    <div class="xkz-main">
        <div>
            <div class="xkz-title">{{ yhytData.companyName }}</div>
            <img class="images" :src="filePath" @click="previewPic" />
        </div>
        <van-divider />
        <div class="text">
            <div class="btn-text mtb10">
                <div class="flex">
                    <div>许可证号：</div>
                    <div class="text-line">{{ yhytData.licNo }}</div>
                </div>
            </div>
            <div class="btn-text mtb10">
                <div class="flex">
                    <div>企业名称：</div>
                    <div class="text-line" style="width: 60vw">{{ yhytData.companyName }}</div>
                </div>
                <div class="btn" v-if="isView == false" @click="handlePositionClick">定位</div>
            </div>
            <div class="flex mtb10">
                <div>法人代表：</div>
                <div class="text-line">{{ yhytData.managerName }}</div>
            </div>
            <div class="flex mtb10" v-if="yhytData.retailTel">
                <div>联系电话：</div>
                <div class="text-line">
                    <span class="phone-link" @click="makePhoneCall(yhytData.retailTel)">{{ yhytData.retailTel }}</span>
                </div>
            </div>
            <div class="flex mtb10">
                <div>企业类型：</div>
                <div class="text-line">{{ yhytData.bizFormat }}</div>
            </div>
            <div class="flex mtb10">
                <div>经营场所：</div>
                <div class="text-line">{{ yhytData.businessAddr }}</div>
            </div>
            <div class="flex mtb10">
                <div>许可范围：</div>
                <div class="text-line">{{ yhytData.managerScopeName }}</div>
            </div>
            <div class="flex mtb10">
                <div>客户编码：</div>
                <div class="text-line">{{ yhytData.custCode }}</div>
            </div>
            <div class="flex mtb10">
                <div>客户档位：</div>
                <div class="text-line">{{ yhytData.tapPosition }}</div>
            </div>
            <div class="flex mtb10">
                <div>订货周期：</div>
                <div class="text-line">{{ yhytData.orderWeekday }}</div>
            </div>
            <div class="flex mtb10">
                <div>供货单位：</div>
                <div class="text-line">{{ yhytData.supplyCompanyName }}</div>
            </div>
            <div class="flex mtb10">
                <div>有效期限：</div>
                <div class="text-line" v-if="yhytData.validateStart && yhytData.validateEnd">
                    {{ yhytData.validateStart }}至{{ yhytData.validateEnd }}
                </div>
                <div class="text-line" v-else>无</div>
            </div>
            <div class="flex mtb10">
                <div>营业执照编码：</div>
                <div class="text-line">{{ yhytData.businessLicNo }}</div>
            </div>
            <div class="flex mtb10">
                <div>营业执照有效期：</div>
                <div class="text-line" v-if="yhytData.businessValidStart && yhytData.businessValidEnd">
                    {{ yhytData.businessValidStart }}至{{ yhytData.businessValidEnd }}
                </div>
                <div class="text-line" v-else>无</div>
            </div>
        </div>

        <!-- 地图选择器组件 -->
        <location-map-selector
            v-model:visible="showLocationMap"
            :original-lat="originalLat"
            :original-lon="originalLon"
            :license-info="licData"
            @confirm="handleLocationConfirm"
        />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Divider as VanDivider, showSuccessToast, showFailToast, showImagePreview, showToast } from 'vant';
import { useLicenseStore } from '@/stores/license'
import { http } from '@/utils/http'
import LocationMapSelector from '@/components/common/LocationMapSelector.vue'
import * as dd from 'dingtalk-jsapi'

const emit = defineEmits(['update-location'])

const props = defineProps({
    yhytData: {
        type: Object,
        default: () => ({})
    },
    filePath: {
        type: String,
        default: ''
    },
    isView: {
        type: Boolean,
        default: false
    }
});
const licData = ref({})
const explorationId = ref('')

// 地图选择器相关变量
const showLocationMap = ref(false)
const originalLat = ref(null)
const originalLon = ref(null)

// 定位按钮点击事件
const handlePositionClick = () => {
  // 每次点击时，先从 store 获取最新数据
  licData.value = licenseStore.currentLicense;

  // 获取当前许可证的位置信息
  const currentLicense = licData.value.yhytLicenseVO;
  console.log('当前许可证信息:', currentLicense);

  // 设置原始位置坐标（高德坐标系 GCJ-02）
  originalLat.value = currentLicense?.latitude;
  originalLon.value = currentLicense?.longitude;

  console.log('设置原始位置坐标 (GCJ-02):', originalLon.value, originalLat.value);

  // 确保坐标有效
  if (!originalLat.value || !originalLon.value) {
    console.warn('警告: 坐标无效，可能导致地图无法正确显示标记点');
  }

  // 显示地图选择器
  showLocationMap.value = true;
};

// 地图选择确认事件
const handleLocationConfirm = async (location) => {
  try {
    console.log('选择的位置:', location);

    // 更新位置信息
    await http.post('/api/dingapp/exploration/submitAndUpdateLocation', {
      explorationId: explorationId.value,
      licenseId: licData.value.yhytLicenseVO.id,
      longitude: location.longitude,
      latitude: location.latitude,
    });

    // 更新 licData 中的经纬度数据
    if (licData.value && licData.value.yhytLicenseVO) {
      console.log('更新前的位置数据:', {
        latitude: licData.value.yhytLicenseVO.latitude,
        longitude: licData.value.yhytLicenseVO.longitude
      });

      // 更新经纬度
      licData.value.yhytLicenseVO.latitude = location.latitude;
      licData.value.yhytLicenseVO.longitude = location.longitude;

      console.log('更新后的位置数据:', {
        latitude: licData.value.yhytLicenseVO.latitude,
        longitude: licData.value.yhytLicenseVO.longitude
      });

      // 更新 licData 中的数据
      console.log('位置数据已更新，下次打开地图将显示新位置');

      // 同时更新 store 中的数据
      if (licenseStore.currentLicense && licenseStore.currentLicense.yhytLicenseVO) {
        licenseStore.currentLicense.yhytLicenseVO.latitude = location.latitude;
        licenseStore.currentLicense.yhytLicenseVO.longitude = location.longitude;
        console.log('store 中的位置数据已更新');
      }
    }

    showSuccessToast('位置更新成功');
  } catch (error) {
    console.error('位置更新失败:', error);
    showFailToast('位置更新失败');
  }
};

const previewPic = () => {
    showImagePreview({
        images: [props.filePath],
        closeable: true
    })
}

// 使用钉钉JSAPI调起手机呼叫功能
const makePhoneCall = (phoneNumber) => {
    if (!phoneNumber) {
        showToast('电话号码不存在');
        return;
    }

    try {
        // 使用钉钉的拨打电话API
        dd.biz.telephone.call({
            phoneNumber: phoneNumber,
            onSuccess: function() {
                console.log('拨打电话成功');
            },
            onFail: function(err) {
                console.error('拨打电话失败:', err);
                // 如果钉钉API调用失败，尝试使用浏览器原生方法
                window.location.href = 'tel:' + phoneNumber;
            }
        });
    } catch (error) {
        console.error('调用钉钉API出错:', error);
        // 出错时尝试使用浏览器原生方法
        window.location.href = 'tel:' + phoneNumber;
    }
}

// 在组件级别获取 licenseStore 实例
const licenseStore = useLicenseStore()

onMounted(async () => {
      // 从 store 中获取数据
      licData.value = licenseStore.currentLicense
      explorationId.value = licenseStore.explorationId
      console.log(licData.value,'licdata')

      // 如果 licData 中有数据，但 store 中没有，则更新 store
      if (licData.value && !licenseStore.currentLicense) {
        licenseStore.setCurrentLicense(licData.value)
      }
})

</script>

<style scoped>
.xkz-main {
    padding: 15px;
    background-color: white;
    height: 100vh;
    overflow-y: auto;
}

.xkz-title {
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    padding: 10px 0 20px;
}

.images {
    width: 70%;
    height: 140px;
    border-radius: 5px;
    display: block;
    margin: 0 auto 15px;
}

.text {
    font-size: 16px;
}

.flex {
    display: flex;
    align-items: center; /* 改为居中对齐，使所有行高度一致 */
    min-height: 24px; /* 设置最小高度，确保所有行高度一致 */
}

.mtb10 {
    margin: 10px 0;
}

.text-line {
    flex: 1;
    word-break: break-all;
    white-space: normal;
    width: 67vw;
}

.phone-link {
    color: #1989fa;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    height: 20px; /* 固定高度与其他行一致 */
    line-height: 20px; /* 行高与高度一致 */
    position: relative;
}

.phone-link:active {
    opacity: 0.7;
}

.phone-link::after {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231989fa'%3E%3Cpath d='M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: middle;
    margin-left: 4px;
}

.btn-text {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.btn {
    background-color: #1c88f9;
    color: white;
    text-align: center;
    border-radius: 12px;
    padding: 6px 8px;
    height: 15px;
    line-height: 15px;
    max-width: 50px;
    min-width: 50px;
    margin-left: 14px;
    display: inline-block;
    font-size: 14px;
}
</style>