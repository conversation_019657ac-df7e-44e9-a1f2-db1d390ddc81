<template>
  <div class="monitoring-container">
    <!-- 顶部筛选区域 -->
    <div class="filter-panel">
      <van-row>
        <van-col span="12">
          <div class="filter-item">
            <span class="filter-label">报表：</span>
            <van-dropdown-menu class="custom-dropdown">
              <van-dropdown-item v-model="timeRange" :options="timeRangeOptions" />
            </van-dropdown-menu>
          </div>
        </van-col>
        <van-col span="12">
          <div class="filter-item">
            <span class="filter-label">时间：</span>
            <template v-if="timeRange === 'day'">
              <div class="date-display" @click="showDingDatePicker">
                {{ formatDate(selectedDateObj) }}
                <van-icon name="calendar-o" />
              </div>
            </template>
            <template v-else-if="timeRange === 'week'">
              <div class="date-display" @click="showDingWeekPicker">
                {{ formatWeek(selectedWeek) }}
                <van-icon name="calendar-o" />
              </div>
            </template>
            <template v-else-if="timeRange === 'month'">
              <div class="date-display" @click="showDingMonthPicker">
                {{ formatMonth(selectedMonth) }}
                <van-icon name="calendar-o" />
              </div>
            </template>
            <template v-else-if="timeRange === 'quarter'">
              <div class="date-display" @click="showDingQuarterPicker">
                {{ formatQuarter(selectedQuarter, selectedYear) }}
                <van-icon name="calendar-o" />
              </div>
            </template>
            <template v-else-if="timeRange === 'year'">
              <div class="date-display" @click="showDingYearPicker">
                {{ formatYear(selectedYear) }}
                <van-icon name="calendar-o" />
              </div>
            </template>
          </div>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="12">
          <div class="filter-item">
            <span class="filter-label">角色：</span>
            <div class="tree-select-wrapper" :class="{ 'disabled-tree-select': !isAdmin }">
              <div class="selected-team" @click="showDeptTreePopup = isAdmin">
                {{ getSelectedTeamText() }}
                <van-icon name="arrow-down" />
              </div>
            </div>

            <!-- 部门树形选择弹出层 -->
            <van-popup v-model:show="showDeptTreePopup" position="bottom" round :style="{ height: '60%' }">
              <div class="popup-header">
                <div class="popup-title">选择角色</div>
                <van-icon name="cross" @click="showDeptTreePopup = false" />
              </div>
              <van-tree-select
                v-model:active-id="selectedTeam"
                v-model:main-active-index="activeTreeIndex"
                :items="deptTreeItems"
                height="calc(100% - 50px)"
                @click-item="handleTreeSelectChange"
              />
            </van-popup>
          </div>
        </van-col>
        <van-col span="12">
          <div class="filter-item">
            <span class="filter-label">人员：</span>
            <van-dropdown-menu class="custom-dropdown" :class="{ 'disabled-dropdown': !isAdmin }">
              <van-dropdown-item v-model="selectedPerson" :options="personOptions" :disabled="!isAdmin" />
            </van-dropdown-menu>
          </div>
        </van-col>
      </van-row>
    </div>

    <!-- 数据概览卡片 -->
    <div class="overview-cards">
      <div class="card">
        <div class="card-title">系统登录人数（人）</div>
        <div class="card-value">{{ overviewData.loginCount }}</div>
        <div class="card-trend" :class="overviewData.loginTrend > 0 ? 'up' : 'down'">
          <van-icon :name="overviewData.loginTrend > 0 ? 'arrow-up' : 'arrow-down'" />
          <span>{{ Math.abs(overviewData.loginTrend) }}%</span>
        </div>
      </div>
      <div class="card">
        <div class="card-title">检查零售户数（户）</div>
        <div class="card-value">{{ overviewData.inspectionCount }}</div>
        <div class="card-trend" :class="overviewData.inspectionTrend > 0 ? 'up' : 'down'">
          <van-icon :name="overviewData.inspectionTrend > 0 ? 'arrow-up' : 'arrow-down'" />
          <span>{{ Math.abs(overviewData.inspectionTrend) }}%</span>
        </div>
      </div>
      <div class="card">
        <div class="card-title">品规识别次数（次）</div>
        <div class="card-value">{{ overviewData.recognitionCount }}</div>
        <div class="card-trend" :class="overviewData.recognitionTrend > 0 ? 'up' : 'down'">
          <van-icon :name="overviewData.recognitionTrend > 0 ? 'arrow-up' : 'arrow-down'" />
          <span>{{ Math.abs(overviewData.recognitionTrend) }}%</span>
        </div>
      </div>
      <div class="card">
        <div class="card-title">识别品规数（个）</div>
        <div class="card-value">{{ overviewData.specCount }}</div>
        <div class="card-trend" :class="overviewData.specTrend > 0 ? 'up' : 'down'">
          <van-icon :name="overviewData.specTrend > 0 ? 'arrow-up' : 'arrow-down'" />
          <span>{{ Math.abs(overviewData.specTrend) }}%</span>
        </div>
      </div>
      <div class="card">
        <div class="card-title">异常品规数（个）</div>
        <div class="card-value">{{ overviewData.abnormalCigaretteCount }}</div>
        <div class="card-trend" :class="overviewData.abnormalCigaretteTrend > 0 ? 'up' : 'down'">
          <van-icon :name="overviewData.abnormalCigaretteTrend > 0 ? 'arrow-up' : 'arrow-down'" />
          <span>{{ Math.abs(overviewData.abnormalCigaretteTrend) }}%</span>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <!-- 人员使用情况排名列表 -->
    <div v-if="showPersonnelRanking" class="chart-section">
      <div class="chart-title">人员使用情况排名</div>
      <div class="table-container">
        <table class="ranking-table">
          <thead>
            <tr>
              <th style="width: 40px">排名</th>
              <th style="width: 65px">中队</th>
              <th style="width: 65px">姓名</th>
              <th style="width: 65px">登录次数</th>
              <th style="width: 90px">检查零售户数</th>
              <th style="width: 90px">品规识别次数</th>
              <th style="width: 90px">识别品规数</th>
            </tr>
          </thead>
          <tbody v-if="loadingRanking">
            <tr>
              <td colspan="7" class="loading-cell">
                <van-loading type="spinner" color="#1989fa" />
              </td>
            </tr>
          </tbody>
          <tbody v-else-if="personnelRanking.length">
            <tr v-for="(item, index) in personnelRanking" :key="index" :class="{ 'even-row': index % 2 === 1 }">
              <td style="width: 40px">{{ item.ranking }}</td>
              <td style="width: 65px">{{ item.deptName }}</td>
              <td style="width: 65px">{{ item.name }}</td>
              <td style="width: 65px">{{ item.loginCount }}</td>
              <td style="width: 90px">{{ item.inspectionCount }}</td>
              <td style="width: 90px">{{ item.recognitionCount }}</td>
              <td style="width: 90px">{{ item.specCount }}</td>
            </tr>
          </tbody>
        </table>
        <div v-if="!loadingRanking && !personnelRanking.length" class="empty-container">
          <van-empty description="暂无数据" image-size="100" />
        </div>
      </div>
    </div>

    <div class="chart-section">
      <div class="chart-title">检查零售户统计（单位：户）</div>
      <div class="chart-container">
        <inspection-chart :chartData="chartData.inspectionData" />
      </div>
    </div>

    <div class="chart-section">
      <div class="chart-title">品规识别统计</div>
      <div class="chart-container">
        <recognition-chart :chartData="chartData.recognitionData" />
      </div>
    </div>

    <div class="chart-section">
      <div class="chart-title">正常/异常品规统计（单位：个）</div>
      <div class="chart-container">
        <cigarette-status-chart :chartData="chartData.cigaretteData" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { showToast, showLoadingToast, closeToast } from 'vant';
import InspectionChart from '@/components/charts/InspectionChart.vue';
import RecognitionChart from '@/components/charts/RecognitionChart.vue';
import CigaretteStatusChart from '@/components/charts/CigaretteStatusChart.vue';
import * as monitoringApi from '@/api/monitoring';
import * as deptApi from '@/api/dept';
import * as dd from 'dingtalk-jsapi';
import { useUserStore } from '@/stores/user';

// 时间范围选项
const timeRange = ref('day');
const timeRangeOptions = [
  { text: '日报表', value: 'day' },
  { text: '周报表', value: 'week' },
  { text: '月报表', value: 'month' },
  { text: '季度报表', value: 'quarter' },
  { text: '年报表', value: 'year' }
];

// 日期选择相关变量
const today = new Date();
const selectedDate = ref(formatDateString(today)); // 字符串格式，用于API请求
const selectedDateObj = ref(new Date()); // Date对象格式，用于日期选择器

// 周选择相关变量
const selectedWeek = ref({
  year: today.getFullYear(),
  week: getWeekOfYear(today)
});

// 月份选择相关变量
const selectedMonth = ref({
  year: today.getFullYear(),
  month: today.getMonth() + 1
});

// 季度选择相关变量
const selectedQuarter = ref(Math.floor(today.getMonth() / 3) + 1);
const selectedYear = ref(today.getFullYear());

// 日期格式化函数
function formatDateString(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 格式化日期显示
function formatDate(dateStr) {
  if (!dateStr) return '';
  // 处理Date对象或字符串
  const date = dateStr instanceof Date ? dateStr : new Date(dateStr);
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
}

// 获取日期所在的周数
function getWeekOfYear(date) {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// 格式化周显示
function formatWeek(weekObj) {
  if (!weekObj) return '';

  // 计算该周的周一和周日日期
  const firstDayOfYear = new Date(weekObj.year, 0, 1);
  const daysOffset = (weekObj.week - 1) * 7;
  const mondayOfWeek = new Date(firstDayOfYear);
  mondayOfWeek.setDate(firstDayOfYear.getDate() + daysOffset - firstDayOfYear.getDay() + 1);

  const sundayOfWeek = new Date(mondayOfWeek);
  sundayOfWeek.setDate(mondayOfWeek.getDate() + 6);

  // 格式化日期为 MM月DD日
  const formatDate = (date) => {
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  };

  return `${formatDate(mondayOfWeek)} - ${formatDate(sundayOfWeek)}`;
}

// 格式化月份显示
function formatMonth(monthObj) {
  if (!monthObj) return '';
  return `${monthObj.year}年${monthObj.month}月`;
}

// 格式化季度显示
function formatQuarter(quarter, year) {
  if (!quarter || !year) return '';
  return `${year}年第${quarter}季度`;
}

// 格式化年份显示
function formatYear(year) {
  if (!year) return '';
  return `${year}年`;
}

// 格式化日期为 yyyy-MM-dd 字符串
function formatYMD(date) {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

// 显示钉钉日期选择器
function showDingDatePicker() {
  dd.biz.util.datepicker({
    format: 'yyyy-MM-dd',
    value: formatYMD(selectedDateObj.value),
    onSuccess: function(result) {
      const pickedDate = new Date(result.value);
      selectedDateObj.value = pickedDate;
      selectedDate.value = formatDateString(pickedDate);
    }
  });
}

// 显示钉钉周选择器
function showDingWeekPicker() {
  // 计算当前选中周的周三作为默认显示日期
  const firstDayOfYear = new Date(selectedWeek.value.year, 0, 1);
  const daysOffset = (selectedWeek.value.week - 1) * 7;
  const mondayOfSelectedWeek = new Date(firstDayOfYear);
  mondayOfSelectedWeek.setDate(firstDayOfYear.getDate() + daysOffset - firstDayOfYear.getDay() + 1);

  const wednesdayOfSelectedWeek = new Date(mondayOfSelectedWeek);
  wednesdayOfSelectedWeek.setDate(mondayOfSelectedWeek.getDate() + 2);

  dd.biz.util.datepicker({
    format: 'yyyy-MM-dd',
    value: formatYMD(wednesdayOfSelectedWeek),
    onSuccess: function(result) {
      const selectedDate = new Date(result.value);
      const year = selectedDate.getFullYear();
      const weekNumber = getWeekOfYear(selectedDate);

      selectedWeek.value = { year, week: weekNumber };
    }
  });
}

// 通用的年份选择函数
function showYearPicker(title, years, callback) {
  dd.device.notification.actionSheet({
    title: title || "选择年份",
    cancelButton: '取消',
    otherButtons: years.map(year => `${year}年`),
    onSuccess: function(result) {
      if (result.buttonIndex === -1) return; // 用户点击了取消
      callback(years[result.buttonIndex]);
    }
  });
}

// 显示钉钉月份选择器
function showDingMonthPicker() {
  const currentYear = selectedMonth.value.year;
  const years = [currentYear - 1, currentYear, currentYear + 1];

  showYearPicker("选择年份", years, (selectedYear) => {
    const months = Array.from({ length: 12 }, (_, i) => `${i + 1}月`);

    dd.device.notification.actionSheet({
      title: `${selectedYear}年 - 选择月份`,
      cancelButton: '取消',
      otherButtons: months,
      onSuccess: function(monthResult) {
        if (monthResult.buttonIndex === -1) return;

        selectedMonth.value = {
          year: selectedYear,
          month: monthResult.buttonIndex + 1
        };
      }
    });
  });
}

// 显示钉钉年份选择器
function showDingYearPicker() {
  const currentYear = selectedYear.value;
  const startYear = currentYear - 2;
  const years = Array.from({ length: 5 }, (_, i) => startYear + i);

  showYearPicker("选择年份", years, (year) => {
    selectedYear.value = year;
  });
}

// 显示钉钉季度选择器
function showDingQuarterPicker() {
  const currentYear = selectedYear.value;
  const years = [currentYear - 1, currentYear, currentYear + 1];

  showYearPicker("选择年份", years, (year) => {
    const quarters = ['第一季度', '第二季度', '第三季度', '第四季度'];

    dd.device.notification.actionSheet({
      title: `${year}年 - 选择季度`,
      cancelButton: '取消',
      otherButtons: quarters,
      onSuccess: function(quarterResult) {
        if (quarterResult.buttonIndex === -1) return;

        selectedQuarter.value = quarterResult.buttonIndex + 1;
        selectedYear.value = year;
      }
    });
  });
}

// 获取用户信息
const userStore = useUserStore();

// 判断用户是否为管理员
const isAdmin = computed(() => {
  // 获取用户角色信息
  const roleName = userStore.userInfo?.role_name;

  // 如果没有角色信息，则默认为非管理员
  if (!roleName) return false;

  // 定义管理员角色列表
  const adminRoles = ['admin', 'administrator', 'manager'];

  // 将角色字符串拆分为数组（处理可能的多角色情况）
  const userRoles = roleName.split(',');

  // 检查用户的任一角色是否在管理员角色列表中
  return userRoles.some(role => adminRoles.includes(role.trim()));
});

// 部门树形选择相关变量
const showDeptTreePopup = ref(false);
const activeTreeIndex = ref(0);
const deptTreeItems = ref([]);

// 中队选项
const selectedTeam = ref('all');
const teamOptions = ref([
  { text: '全部', value: 'all' }
]);

// 获取选中的中队名称
const getSelectedTeamText = () => {
  if (selectedTeam.value === 'all') {
    return '全部';
  }

  // 在树形结构中查找选中的部门
  for (const category of deptTreeItems.value) {
    if (category.children) {
      const found = category.children.find(item => item.id === selectedTeam.value);
      if (found) {
        return found.text;
      }
    }
  }

  // 如果在树形结构中没找到，则在原来的teamOptions中查找
  const found = teamOptions.value.find(item => item.value === selectedTeam.value);
  return found ? found.text : '全部';
};

// 处理树形选择变化
const handleTreeSelectChange = (item) => {
  selectedTeam.value = item.id;
  showDeptTreePopup.value = false;

  // 无论选择哪个中队，都重置人员选择为"全部人员"
  selectedPerson.value = 'all';

  // 加载该中队下的人员列表
  loadPersonnel(item.id).then(() => {
    fetchData(); // 加载完人员列表后刷新数据
  });
};

// 人员选项
const selectedPerson = ref('all');
const personOptions = ref([
  { text: '全部人员', value: 'all' }
]);

// 概览数据
const overviewData = ref({
  loginCount: 0,
  loginTrend: 0,
  inspectionCount: 0,
  inspectionTrend: 0,
  recognitionCount: 0,
  recognitionTrend: 0,
  specCount: 0,
  specTrend: 0,
  abnormalCigaretteCount: 0,
  abnormalCigaretteTrend: 0
});

// 图表数据
const chartData = ref({
  inspectionData: {
    xAxis: [],
    series: []
  },
  recognitionData: {
    xAxis: [],
    series: []
  },
  cigaretteData: {
    xAxis: [],
    series: []
  }
});

// 人员排名数据
const personnelRanking = ref([]);
const loadingRanking = ref(false);
// 是否显示人员排名表格（只在选择了特定中队且未选择特定人员时显示）
// 如果选择了特定人员，则不显示排名表格（因为只有一个人没有排名意义）
const showPersonnelRanking = computed(() =>
  selectedPerson.value === 'all'
);



// 监听筛选条件变化，自动刷新数据
watch(timeRange, () => {
  fetchData();
});

watch(selectedPerson, () => {
  fetchData();
});

// 监听日期相关变量变化
watch(selectedDate, (newVal) => {
  if (timeRange.value === 'day') {
    // 同步更新Date对象
    if (newVal && selectedDateObj.value.toDateString() !== new Date(newVal).toDateString()) {
      selectedDateObj.value = new Date(newVal);
    }
    fetchData();
  }
});

watch(selectedWeek, () => {
  if (timeRange.value === 'week') {
    fetchData();
  }
}, { deep: true });

watch(selectedMonth, () => {
  if (timeRange.value === 'month') {
    fetchData();
  }
}, { deep: true });

watch(selectedQuarter, () => {
  if (timeRange.value === 'quarter') {
    fetchData();
  }
});

watch(selectedYear, () => {
  if (['year', 'quarter'].includes(timeRange.value)) {
    fetchData();
  }
});

// 处理中队选择变化
const handleTeamChange = (value) => {
  // 只有管理员可以更改选择
  if (!isAdmin.value) return;

  // 无论选择哪个中队，都重置人员选择为"全部人员"
  selectedPerson.value = 'all';

  if (value === 'all') {
    fetchData(); // 当选择"全部"时直接刷新数据
    return;
  }

  // 加载该中队下的人员列表
  loadPersonnel(value).then(() => {
    fetchData(); // 加载完人员列表后刷新数据
  });
};

// 通用的数据加载函数
async function loadData(loadFn, errorMsg) {
  try {
    showLoadingToast({ message: '加载中...', forbidClick: true });

    try {
      return await loadFn();
    } catch (apiError) {
      console.warn(`API调用失败: ${errorMsg}`, apiError);
      closeToast();
      showToast(errorMsg);
      return null;
    } finally {
      closeToast();
    }
  } catch (error) {
    console.error(`${errorMsg}:`, error);
    closeToast();
    showToast(errorMsg);
    return null;
  }
}

// 加载中队列表
const loadTeams = async () => {
  await loadData(
    async () => {
      // 获取原始中队列表（用于兼容旧的下拉菜单逻辑）
      const response = await monitoringApi.getDeptList();
      teamOptions.value = [
        { text: '全部', value: 'all' },
        ...response.data.map(team => ({
          text: team.deptName,
          value: team.id.toString()
        }))
      ];

      // 获取部门树形结构
      try {
        // 不需要传递tenantId参数，接口会自动从当前登录用户中获取
        const treeResponse = await deptApi.getDeptTreeByParent("1916386099033677825");
        if (treeResponse.code === 200 && treeResponse.data) {
          // 处理树形结构数据，适配TreeSelect组件格式
          const allOption = {
            text: '全部',
            id: 'all'
          };

          // 将部门数据转换为TreeSelect需要的格式
          const formattedItems = treeResponse.data.map(item => {
            // 获取子部门
            const children = item.children ? item.children.map(child => ({
              text: child.deptName,
              id: child.id.toString(),
              disabled: false
            })) : [];

            return {
              text: item.deptName,
              children: [{text: item.deptName + '-全部', id: item.id }, ...children],
            };
          });

          // 添加"全部"选项
          deptTreeItems.value = [
            {
              text: '全部',
              children: [allOption]
            },
            ...formattedItems
          ];
        }
      } catch (error) {
        console.error('获取部门树形结构失败:', error);
        showToast('获取部门树形结构失败');
      }
    },
    '加载中队列表失败'
  );
};

// 加载人员列表
const loadPersonnel = async (deptId) => {
  return await loadData(
    async () => {
      const response = await monitoringApi.getUserList(deptId);
      personOptions.value = [
        { text: '全部人员', value: 'all' },
        ...response.data.map(person => ({ text: person.name, value: person.id.toString() }))
      ];
    },
    '加载人员列表失败'
  );
};

// 获取监控数据
const fetchData = async () => {
  // 构建请求参数
  let params = {
    timeRange: timeRange.value
  };

  // 根据用户权限设置查询参数
  if (isAdmin.value) {
    // 管理员可以查看所有数据
    params.deptId = selectedTeam.value === 'all' ? null : selectedTeam.value;
    params.userId = selectedPerson.value === 'all' ? null : selectedPerson.value;
  } else {
    // 非管理员只能查看自己的数据
    const userId = userStore.userInfo?.user_id;
    if (userId) {
      params.userId = userId.toString();

      // 如果用户有选择特定中队，使用该中队ID
      if (selectedTeam.value !== 'all') {
        params.deptId = selectedTeam.value;
      } else {
        // 否则使用用户的第一个部门ID作为中队ID
        const userDeptIds = userStore.userInfo.dept_id ? userStore.userInfo.dept_id.split(',') : [];
        if (userDeptIds.length > 0) {
          params.deptId = userDeptIds[0].trim();
        }
      }
    }
  }

  // 根据不同的报表类型添加日期参数
  switch (timeRange.value) {
    case 'day':
      // 添加开始时间和结束时间
      params.startTime = selectedDate.value + " 00:00:00";
      params.endTime = selectedDate.value + " 23:59:59";
      break;
    case 'week':
      // 计算周的开始时间（周一）和结束时间（周日）
      const firstDayOfYear = new Date(selectedWeek.value.year, 0, 1);
      const daysOffset = (selectedWeek.value.week - 1) * 7;
      const mondayOfWeek = new Date(firstDayOfYear);
      mondayOfWeek.setDate(firstDayOfYear.getDate() + daysOffset - firstDayOfYear.getDay() + 1);

      const sundayOfWeek = new Date(mondayOfWeek);
      sundayOfWeek.setDate(mondayOfWeek.getDate() + 6);

      // 格式化为 YYYY-MM-DD 格式，并添加时间部分
      params.startTime = formatYMD(mondayOfWeek) + " 00:00:00";
      params.endTime = formatYMD(sundayOfWeek) + " 23:59:59";
      break;

    case 'month':
      // 计算月的开始时间（1号）和结束时间（月末）
      const firstDayOfMonth = new Date(selectedMonth.value.year, selectedMonth.value.month - 1, 1);
      const lastDayOfMonth = new Date(selectedMonth.value.year, selectedMonth.value.month, 0);

      params.startTime = formatYMD(firstDayOfMonth) + " 00:00:00";
      params.endTime = formatYMD(lastDayOfMonth) + " 23:59:59";
      break;

    case 'quarter':
      // 计算季度的开始月份和结束月份
      const startMonth = (selectedQuarter.value - 1) * 3;
      const endMonth = startMonth + 2;

      // 计算季度的开始时间和结束时间
      const firstDayOfQuarter = new Date(selectedYear.value, startMonth, 1);
      const lastDayOfQuarter = new Date(selectedYear.value, endMonth + 1, 0);

      params.startTime = formatYMD(firstDayOfQuarter) + " 00:00:00";
      params.endTime = formatYMD(lastDayOfQuarter) + " 23:59:59";
      break;

    case 'year':
      // 计算年的开始时间（1月1日）和结束时间（12月31日）
      const firstDayOfSelectedYear = new Date(selectedYear.value, 0, 1);
      const lastDayOfSelectedYear = new Date(selectedYear.value, 11, 31);

      params.startTime = formatYMD(firstDayOfSelectedYear) + " 00:00:00";
      params.endTime = formatYMD(lastDayOfSelectedYear) + " 23:59:59";
      break;
  }

  await loadData(
    async () => {
      // 并行获取所有数据
      const [monitoringResponse, inspectionResponse, recognitionResponse, cigaretteResponse] = await Promise.all([
        monitoringApi.getMonitoringData(params),
        monitoringApi.getInspectionData(params),
        monitoringApi.getRecognitionData(params),
        monitoringApi.getCigaretteData(params)
      ]);

      // 更新数据
      overviewData.value = monitoringResponse.data;

      // 确保图表数据包含 xAxis
      chartData.value.inspectionData = {
        xAxis: inspectionResponse.data.xaxis,
        series: inspectionResponse.data.series || []
      };

      chartData.value.recognitionData = {
        xAxis: recognitionResponse.data.xaxis,
        series: recognitionResponse.data.series || []
      };

      chartData.value.cigaretteData = {
        xAxis: cigaretteResponse.data.xaxis,
        series: cigaretteResponse.data.series || []
      };

      // 如果选择了特定中队且未选择特定人员，获取人员排名数据
      if (selectedPerson.value === 'all') {
        loadingRanking.value = true;
        try {
          const rankingResponse = await monitoringApi.getPersonnelRanking(params);
          personnelRanking.value = rankingResponse.data || [];
        } catch (error) {
          console.error('获取人员排名数据失败:', error);
          personnelRanking.value = [];
        } finally {
          loadingRanking.value = false;
        }
      } else {
        // 如果选择了"全部"或选择了特定人员，清空排名数据
        personnelRanking.value = [];
      }
    },
    '获取数据失败'
  );
};

// 组件挂载时加载数据
onMounted(async () => {
  // 先加载中队列表
  await loadTeams();

  // 如果不是管理员，设置为用户自己的中队和个人
  if (!isAdmin.value && userStore.userInfo) {
    // 获取用户所属中队ID (使用dept_id)
    const userDeptIds = userStore.userInfo.dept_id ? userStore.userInfo.dept_id.split(',') : [];
    const userId = userStore.userInfo.user_id;

    console.info('用户部门IDs:', userDeptIds)
    console.info('可用中队选项:', teamOptions.value)

    if (userDeptIds.length > 0) {
      // 查找用户所属的第一个有效中队
      let foundTeam = false;

      // 遍历用户的所有角色ID，找到第一个匹配的中队
      for (const deptId of userDeptIds) {
        const trimmedDeptId = deptId.trim();
        console.info('检查部门ID:', trimmedDeptId);
        const userTeam = teamOptions.value.find(team => team.value === trimmedDeptId);

        if (userTeam) {
          console.info('找到匹配的中队:', userTeam);
          selectedTeam.value = userTeam.value;
          foundTeam = true;
          break;
        }
      }

      // 如果没有找到匹配的中队，保持默认值
      if (!foundTeam) {
        console.info('未找到用户所属中队');
        return;
      }

      // 加载该中队下的人员列表
      await loadPersonnel(selectedTeam.value);

      // 设置为用户自己
      if (userId) {
        const userPerson = personOptions.value.find(person => person.value === userId.toString());
        if (userPerson) {
          selectedPerson.value = userPerson.value;
        }
      }
    }
  }

  // 获取数据
  fetchData();
});
</script>

<style lang="scss" scoped>
.monitoring-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 16px;
}

.filter-panel {
  background-color: #ffffff;
  border-radius: 12px; /* 增加圆角 */
  padding: 5px; /* 增加内边距 */
  margin-bottom: 20px; /* 增加下边距 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08); /* 更柔和的阴影 */
  border: 1px solid #f0f0f0; /* 添加细边框 */
}

.filter-item {
  display: flex;
  align-items: center;
  position: relative; /* 用于定位标签 */
  // background-color: #fafafa; /* 轻微的背景色 */
  border-radius: 6px; /* 圆角 */
  padding: 6px 4px; /* 减小内边距 */
  transition: all 0.3s ease; /* 平滑过渡效果 */
}

.filter-label {
  font-size: 13px; /* 减小字体大小 */
  font-weight: 500; /* 加粗 */
  margin-right: 0px; /* 减小右边距 */
  white-space: nowrap;
  color: #333; /* 更深的颜色 */
}

.custom-dropdown {
  flex: 1; /* 平均分配空间 */
  min-width: 120px; /* 减小最小宽度 */
  white-space: nowrap; /* 防止文本换行 */
  max-width: 100%; /* 确保不超出容器 */

  ::v-deep(.van-dropdown-menu__bar) {
    height: 32px; /* 减小高度 */
    box-shadow: none;
    background-color: #ffffff; /* 白色背景 */
    border: 1px solid #e8e8e8; /* 添加边框 */
    border-radius: 4px; /* 减小圆角 */
    justify-content: flex-start; /* 左对齐下拉菜单标题 */
    transition: all 0.3s ease; /* 平滑过渡效果 */
  }

  ::v-deep(.van-dropdown-menu__bar:hover) {
    border-color: #1989fa; /* 悬停时边框颜色变化 */
  }

  ::v-deep(.van-dropdown-menu__title) {
    font-size: 13px; /* 减小字体大小 */
    padding: 0 8px; /* 减小内边距 */
    text-align: left; /* 确保文本左对齐 */
    color: #333; /* 更深的颜色 */
    overflow: hidden;
    text-overflow: ellipsis; /* 文本溢出时显示省略号 */
    line-height: 32px; /* 与高度保持一致 */
  }

  ::v-deep(.van-dropdown-item__option) {
    text-align: left;
    justify-content: flex-start;
    padding: 8px 12px; /* 减小内边距 */
    font-size: 13px; /* 减小字体大小 */
  }

  ::v-deep(.van-dropdown-item__option--active) {
    color: #1989fa; /* 选中项的颜色 */
  }

  ::v-deep(.van-dropdown-item__content),
  ::v-deep(.van-dropdown-item__title) {
    text-align: left;
  }
}

/* 禁用状态的下拉菜单样式 */
.disabled-dropdown {
  ::v-deep(.van-dropdown-menu__bar) {
    background-color: #f5f5f5; /* 浅灰色背景 */
    border-color: #e0e0e0; /* 更浅的边框 */
    cursor: not-allowed;
  }

  ::v-deep(.van-dropdown-menu__title) {
    color: #999; /* 灰色文本 */
  }

  ::v-deep(.van-dropdown-menu__title::after) {
    display: none; /* 隐藏下拉箭头 */
  }

  ::v-deep(.van-dropdown-menu__bar:hover) {
    border-color: #e0e0e0; /* 禁用悬停效果 */
  }
}

/* 树形选择器样式 */
.tree-select-wrapper {
  flex: 1;
  min-width: 120px;
  white-space: nowrap;
  max-width: 100%;
}

.selected-team {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 8px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selected-team:hover {
  border-color: #1989fa;
}

.disabled-tree-select .selected-team {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.disabled-tree-select .selected-team:hover {
  border-color: #e0e0e0;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.popup-header .van-icon {
  font-size: 20px;
  color: #969799;
  cursor: pointer;
}

.date-display {
  display: flex;
  align-items: center;
  height: 32px; /* 减小高度，与下拉菜单一致 */
  padding: 0 8px; /* 减小内边距 */
  background-color: #ffffff; /* 白色背景 */
  border: 1px solid #e8e8e8; /* 添加边框 */
  border-radius: 4px; /* 减小圆角，与下拉菜单一致 */
  font-size: 13px; /* 减小字体大小，与下拉菜单一致 */
  color: #333; /* 更深的颜色 */
  cursor: pointer;
  min-width: 100px; /* 减小最小宽度，与下拉菜单一致 */
  white-space: nowrap;
  justify-content: center;
  flex: 1; /* 平均分配空间 */
  overflow: hidden; /* 防止内容溢出 */
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
  transition: all 0.3s ease; /* 平滑过渡效果 */

}

.date-display:hover {
  border-color: #1989fa; /* 悬停时边框颜色变化 */
}

.date-display .van-icon {
  margin-left: 8px; /* 增加左边距 */
  color: #1989fa; /* 图标颜色 */
  flex-shrink: 0; /* 防止图标被压缩 */
}

.ml-8 {
  margin-left: 8px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    grid-template-columns: repeat(5, 1fr);
  }

  @media (max-width: 767px) {
    .card:last-child {
      grid-column: span 2;
    }
  }
}

.card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  position: relative;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;

  &.up {
    color: #f56c6c;
  }

  &.down {
    color: #67c23a;
  }

  .van-icon {
    margin-right: 4px;
  }
}

.chart-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-left: 8px;
  border-left: 3px solid #1989fa;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.table-container {
  width: 100%;
  overflow-x: auto; /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 提升iOS滚动体验 */
  margin-bottom: 16px;
  display: block; /* 确保是块级元素 */
  position: relative; /* 建立定位上下文 */
  max-width: 100%; /* 确保不超出父容器 */
  scrollbar-width: thin; /* 细滚动条（Firefox） */
}

.ranking-table {
  width: 100%;
  min-width: 620px; /* 设置最小宽度，确保能容纳所有列 */
  border-collapse: collapse;
  table-layout: fixed; /* 固定表格布局 */

  th, td {
    padding: 12px 16px;
    text-align: center;
    white-space: nowrap;
    box-sizing: border-box;
  }

  th {
    background-color: #f7f8fa;
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    border-bottom: 1px solid #ebedf0;
    position: sticky; /* 使表头固定 */
    top: 0; /* 固定在顶部 */
    z-index: 1; /* 确保表头在内容之上 */
  }

  td {
    font-size: 14px;
    color: #646566;
    border-bottom: 1px solid #ebedf0;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .even-row {
    background-color: #fafafa;
  }

  tr:last-child td {
    border-bottom: none;
  }
}

/* 自定义滚动条样式（Webkit浏览器） */
.table-container::-webkit-scrollbar {
  height: 6px; /* 水平滚动条高度 */
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景 */
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 滚动条滑块颜色 */
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* 悬停时滑块颜色 */
}

.loading-cell {
  height: 200px;
  text-align: center;
  vertical-align: middle;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}


</style>
