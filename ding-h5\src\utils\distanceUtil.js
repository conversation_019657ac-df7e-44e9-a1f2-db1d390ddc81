/**
 * 计算两个经纬度坐标点之间的距离（单位：米）
 * @param {number} lat1 - 第一个点的纬度
 * @param {number} lon1 - 第一个点的经度
 * @param {number} lat2 - 第二个点的纬度
 * @param {number} lon2 - 第二个点的经度
 * @returns {number} - 两点之间的距离（米）
 */
export function calculateDistance(lat1, lon1, lat2, lon2) {
  // 地球半径（单位：米）
  const R = 6371000

  // 将经纬度转换为弧度
  const rad1 = (lat1 * Math.PI) / 180
  const rad2 = (lat2 * Math.PI) / 180
  const deltaLat = ((lat2 - lat1) * Math.PI) / 180
  const deltaLon = ((lon2 - lon1) * Math.PI) / 180

  // 使用Haversine公式计算距离
  const a =
    Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(rad1) *
      Math.cos(rad2) *
      Math.sin(deltaLon / 2) *
      Math.sin(deltaLon / 2)

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c

  return distance
}