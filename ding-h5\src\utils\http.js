import axios from 'axios'
import { Toast, showFailToast } from 'vant'
import { useUserStore } from '@/stores/user'

export const http = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'Blade-Requested-With': 'BladeHttpRequest'
  }
})

http.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    if (userStore.accessToken) {
      config.headers['Blade-Auth'] = 'bearer ' + userStore.accessToken
    }
    config.headers['Authorization'] = 'Basic ' + btoa('rider:rider_secret')

    // 处理POST请求数据
    if (config.method === 'post' && config.data && !(config.data instanceof FormData)) {
      config.data = JSON.stringify(config.data)
    }

    // 处理GET请求参数
    if (config.method === 'get' && config.params) {
      const params = new URLSearchParams(config.params)
      config.url = `${config.url}${config.url.includes('?') ? '&' : '?'}${params.toString()}`
      config.params = undefined
    }

    // 开发环境下处理URL
    // if (process.env.NODE_ENV === 'development') {
    //   const url = config.url
    //   if (url.startsWith('/api/')) {
    //     config.url = url.substring(4)
    //   }
    // }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

http.interceptors.response.use(
  response => {
    // 处理文件下载
    if (response.config.responseType === 'blob') {
      const contentType = response.headers['content-type']
      if (contentType && contentType.includes('application/json')) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = () => {
            const result = JSON.parse(reader.result)
            if (result.code !== 200) {
              showFailToast(result.msg || '请求失败')
              reject(result)
            } else {
              resolve(response)
            }
          }
          reader.readAsText(response.data)
        })
      }
      return response
    }

    if (response.data.access_token || response.data.key) {
      return response.data
    }
    if (response.data.code !== 200) {
      showFailToast(response.data.msg || '请求失败')
      return Promise.reject(response)
    }
    return response.data
  },
  error => {
    if (error.response?.data) {
      showFailToast(error.response.data)
    }
    if (error.response?.status === 401) {
      const userStore = useUserStore()
      userStore.isLogin = false
      // TODO: 跳转到登录页
    }
    return Promise.reject(error)
  }
)