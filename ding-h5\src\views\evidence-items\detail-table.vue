<template>
  <div class="detail-table-container">
    <div class="header">
      <div class="back-button" @click="goBack">
        <van-icon name="arrow-left" size="20" />
      </div>
      <div class="title">{{ title || '涉案物品详情' }}</div>
      <div class="action-buttons">
        <div class="copy-button" @click="copyFormattedText">
          <van-icon name="description" size="20" />
        </div>
        <div class="screenshot-button" @click="takeScreenshot">
          <van-icon name="photograph" size="20" />
        </div>
      </div>
    </div>

    <van-cell-group class="section" title="物品列表">
      <div class="table-container">
        <div class="table-header">
          <div class="col-item" style="width: 50px" size="normal">序号</div>
          <div class="col-item" style="width: 250px">品规</div>
          <div class="col-item" style="width: 150px">条形码</div>
          <div class="col-item" style="width: 60px">数量</div>
          <div class="col-item" style="width: 100px">单价</div>
          <div class="col-item" style="width: 100px">金额</div>
        </div>

        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" />
        </div>

        <template v-else-if="items.length">
          <div class="table-body">
            <div v-for="(item, index) in items" :key="index" class="table-row">
              <div class="col-item" style="width: 50px" size="normal">{{ index + 1 }}</div>
              <div class="col-item" style="width: 250px">{{ item.productName }}</div>
              <div class="col-item" style="width: 150px">{{ item.barcode || '-' }}</div>
              <div class="col-item" style="width: 60px">{{ item.quantity }}</div>
              <div class="col-item" style="width: 100px">{{ Number(item.price).toFixed(2) }}{{ item.priceUnit }}</div>
              <div class="col-item" style="width: 100px">{{ (Number(item.price) * Number(item.quantity)).toFixed(2) }}</div>
            </div>
          </div>
        </template>
        <van-empty v-else description="暂无数据" image-size="100" />
      </div>
    </van-cell-group>

    <van-cell-group class="section" title="统计信息">
      <van-cell title="品规数" :value="items.length + '个'" />
      <van-cell title="总条数" :value="totalQuantity + '条'" />
      <van-cell title="总金额" :value="'¥' + totalAmount + '元'" />
    </van-cell-group>

    <van-cell-group class="section" title="案件信息" v-if="caseInfo.enforcementAgency || caseInfo.partyInvolved">
      <van-cell title="执法机构" :value="caseInfo.enforcementAgency" v-if="caseInfo.enforcementAgency" />
      <van-cell title="案发时间" :value="caseInfo.caseTime" v-if="caseInfo.caseTime" />
      <van-cell title="地址" :value="caseInfo.address + caseInfo.detailedAddress" v-if="caseInfo.address || caseInfo.detailedAddress" />
      <van-cell title="联合执法单位" :value="caseInfo.jointEnforcementAgency" v-if="caseInfo.jointEnforcementAgency" />
      <van-cell title="案由" :value="caseInfo.caseReason" v-if="caseInfo.caseReason" />
      <van-cell title="当事人" :value="caseInfo.partyInvolved" v-if="caseInfo.partyInvolved" />
      <van-cell title="许可证号" :value="caseInfo.licenseNo" v-if="caseInfo.licenseNo" />
    </van-cell-group>

    <!-- 模板选择弹窗 -->
    <van-dialog
      v-model:show="showTemplateDialog"
      title="选择复制模板"
      :show-confirm-button="false"
      close-on-click-overlay
      class="template-dialog"
      width="80%"
      :style="{ maxWidth: '500px' }"
    >
      <div class="template-list-container">
        <div class="template-list">
          <!-- 默认导出按钮 -->
          <van-button
            type="primary"
            block
            class="template-button default-template"
            @click="copyDefaultText(); showTemplateDialog = false;"
          >
            默认格式
          </van-button>

          <!-- 分隔线 -->
          <div class="template-divider">其他模板</div>

          <!-- 其他模板按钮 -->
          <van-button
            v-for="template in templateList"
            :key="template.id"
            type="primary"
            block
            class="template-button"
            @click="copyWithTemplate(template.template); showTemplateDialog = false;"
          >
            {{ template.name }}
          </van-button>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showFailToast, closeToast, showSuccessToast } from 'vant'
import { http } from '@/utils/http'
import * as dd from 'dingtalk-jsapi'

// We'll dynamically import html2canvas when needed
// This avoids the "failed to resolve import" error if the package isn't installed

const route = useRoute()
const router = useRouter()
const selectionTime = route.query.selectionTime || ''
const title = ref(route.query.title || '')
const items = ref([])
const loading = ref(true)

// 案件相关信息
const caseInfo = ref({
  enforcementAgency: '',       // 执法机构
  caseTime: '',                // 案发时间
  address: '',                 // 地址
  detailedAddress: '',         // 详细地址
  jointEnforcementAgency: '',  // 联合执法单位
  caseReason: '',              // 案由
  partyInvolved: '',           // 当事人
  licenseNo: ''                // 许可证号
})

// 计算总数量和总金额
const totalQuantity = computed(() => {
  // 使用更精确的方法计算总数量，避免浮点数精度问题
  const total = items.value.reduce((sum, item) => sum + Number(item.quantity), 0);
  // 四舍五入到一位小数
  return Math.round(total * 10) / 10;
})

const totalAmount = computed(() => {
  return items.value.reduce((sum, item) => sum + Number(item.price) * Number(item.quantity), 0).toFixed(2)
})

// 返回上一页
const goBack = () => {
  // 先尝试恢复竖屏，然后返回上一页
  resetScreenOrientation()
  setTimeout(() => {
    router.back()
  }, 100) // 稍微延迟一下，确保屏幕旋转命令已发出
}

// 加载数据
const loadData = async () => {
  if (!selectionTime) {
    showFailToast('缺少必要参数')
    loading.value = false
    return
  }

  try {
    const res = await http.get(`/api/dingapp/ecidenceYhyt/detailList?selectionTime=${selectionTime}`)

    if (res.data) {
      // 如果有标题数据，设置标题
      if (res.data.title && !title.value) {
        title.value = res.data.title
      }

      // 处理物品数据
      const itemsData = res.data.items || res.data // 兼容两种数据结构
      const data = itemsData.map(item => ({
        id: item.priceStandardsId,
        price: item.currentUnitPrice,
        priceUnit: item.priceUnit,
        quantity: item.selectedQuantity,
        stdType: item.stdType,
        productName: item.productName,
        barcode: item.barcode || item.itemCode || ''
      }))

      items.value = data

      // 提取案件相关信息（从第一个物品中获取，因为每个物品都包含相同的案件信息）
      if (itemsData.length > 0) {
        const firstItem = itemsData[0]

        // 处理案发时间格式
        let formattedCaseTime = firstItem.caseTime || '';

        // 如果案发时间不是"年月日时"格式，则转换
        if (formattedCaseTime && !formattedCaseTime.includes('年')) {
          try {
            const [datePart, timePart] = formattedCaseTime.split(' ');
            if (datePart && timePart) {
              const [year, month, day] = datePart.split('-');
              const hour = timePart.split(':')[0];
              formattedCaseTime = `${year}年${parseInt(month)}月${parseInt(day)}日${parseInt(hour)}时`;
            }
          } catch (error) {
            console.error('格式化案发时间失败:', error);
          }
        }

        caseInfo.value = {
          enforcementAgency: firstItem.enforcementAgency || '',
          caseTime: formattedCaseTime,
          address: firstItem.address || '',
          detailedAddress: firstItem.detailedAddress || '',
          jointEnforcementAgency: firstItem.jointEnforcementAgency || '',
          caseReason: firstItem.caseReason || '',
          partyInvolved: firstItem.partyInvolved || '',
          licenseNo: firstItem.licenseNo || ''
        }
      }
    } else {
      showFailToast(res.msg || '加载数据失败')
    }
  } catch (error) {
    showFailToast('加载数据失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}


// 设置横屏显示
const setLandscapeMode = () => {
  try {
    if (dd && dd.device && dd.device.screen) {
      dd.rotateScreenView({
        clockwise: true,
        showStatusBar: true,
        success: () => {},
        fail: () => {},
        complete: () => {},
      });
    } else {
      console.warn('当前环境不支持屏幕旋转')
    }
  } catch (error) {
    console.error('设置横屏失败:', error)
  }
}

// 恢复竖屏显示
const resetScreenOrientation = () => {
  try {
    if (dd && dd.device && dd.device.screen ) {
      dd.resetScreenView({
        success: () => {},
        fail: () => {},
        complete: () => {},
      });
    }
  } catch (error) {
    console.error('恢复竖屏失败:', error)
  }
}

// beforeunload事件处理函数
const handleBeforeUnload = () => {
  resetScreenOrientation()
}

// 添加页面卸载事件监听器
const addUnloadListener = () => {
  window.addEventListener('beforeunload', handleBeforeUnload)
}

// 可见性变化处理函数
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // 页面重新变为可见时，设置横屏模式
    setTimeout(() => {
      setLandscapeMode()
    }, 300)
  }
}

// 添加可见性变化监听器
const addVisibilityChangeListener = () => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
}

onMounted(() => {
  loadData()
  // 设置横屏模式
  setTimeout(() => {
    setLandscapeMode()
  }, 300) // 稍微延迟一下，确保页面已经加载

  // 添加事件监听器
  addVisibilityChangeListener()
  addUnloadListener()
  getTemplateDictData()
})

// 页面卸载时恢复竖屏
onUnmounted(() => {
  // 移除事件监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // 尝试多次恢复竖屏，增加成功率
  resetScreenOrientation()
  setTimeout(() => {
    resetScreenOrientation()
  }, 100)
})


// 截图功能
const takeScreenshot = async () => {
  try {
    console.log('开始生成截图...');
    showToast({
      type: 'loading',
      message: '正在生成截图...',
      forbidClick: true,
      duration: 0
    })

    // 动态导入html2canvas
    let html2canvas;
    try {
      console.log('尝试加载html2canvas...');
      html2canvas = (await import('html2canvas')).default;
      console.log('html2canvas加载成功');
    } catch (importError) {
      console.error('加载html2canvas失败:', importError);
      closeToast();
      showFailToast('截图功能需要html2canvas库支持，请先安装该库');
      return;
    }

    // 等待下一帧渲染完成
    await nextTick()
    console.log('开始获取元素...');

    // 获取要截图的元素
    const element = document.querySelector('.detail-table-container')

    if (!element) {
      console.error('找不到目标元素');
      closeToast()
      showFailToast('截图失败：找不到目标元素')
      return
    }

    console.log('元素获取成功，开始生成截图...');
    console.log('元素尺寸:', element.offsetWidth, 'x', element.offsetHeight);

    // 使用html2canvas生成截图
    const canvas = await html2canvas(element, {
      useCORS: true,
      scale: 2, // 提高清晰度
      logging: true, // 启用日志
      allowTaint: true,
      backgroundColor: '#ffffff',
      onclone: (documentClone) => {
        // 在克隆的文档上进行任何必要的修改
        console.log('文档克隆成功');
        return documentClone;
      }
    })

    console.log('截图生成成功，开始转换为base64...');

    // 将canvas转换为base64图片
    const imgData = canvas.toDataURL('image/jpeg', 0.9)
    console.log('图片转换成功，base64长度:', imgData.length);

    // 关闭加载提示
    closeToast()

    // 保存原始 canvas 对象，以便备用
    window._lastScreenshotCanvas = canvas;

    // 使用钉钉API保存图片
    console.log('开始保存图片...');
    saveImageToDingTalk(imgData)
  } catch (error) {
    console.error('截图失败:', error)
    closeToast()
    showFailToast('截图失败: ' + (error.message || '未知错误'))
  }
}

// 使用钉钉的图片预览功能显示截图
const saveImageToDingTalk = (imgData) => {
  try {
    console.log('尝试使用钉钉的图片预览功能...');

    // 检查钉钉API可用性
    console.log('dd对象存在:', !!dd);
    console.log('dd.biz存在:', !!(dd && dd.biz));
    console.log('dd.biz.util存在:', !!(dd && dd.biz && dd.biz.util));
    console.log('dd.biz.util.previewImage存在:', !!(dd && dd.biz && dd.biz.util && dd.biz.util.previewImage));

    // 使用钉钉的图片预览功能
    if (dd.biz && dd.biz.util && dd.biz.util.previewImage) {
      console.log('尝试使用 biz.util.previewImage API...');

      // 将截图数据保存到全局变量，以便用户可以多次查看
      window._lastScreenshotData = imgData;

      // 使用钉钉的图片预览功能
      dd.biz.util.previewImage({
        urls: [imgData], // 图片地址数组
        current: imgData, // 当前显示的图片地址
        success: () => {
          console.log('biz.util.previewImage 成功');
          showToast('长按图片可保存到手机');
        },
        fail: (err) => {
          console.error('biz.util.previewImage 失败:', err);
          tryVantPreview(imgData);
        }
      });
    } else {
      // 如果钉钉的预览API不可用，尝试使用Vant的图片预览组件
      console.log('dd.biz.util.previewImage API不可用，尝试使用Vant的图片预览组件...');
      tryVantPreview(imgData);
    }
  } catch (error) {
    console.error('预览截图失败:', error);
    showFailToast('预览截图失败: ' + (error.message || '未知错误'));
  }
}

// 使用Vant的图片预览组件
const tryVantPreview = (imgData) => {
  try {
    console.log('尝试使用Vant的图片预览组件...');

    // 导入Vant的showImagePreview函数
    import('vant').then(({ showImagePreview }) => {
      // 使用Vant的图片预览组件
      showImagePreview({
        images: [imgData],
        closeable: true,
        onClose: () => {
          showToast('长按图片可保存到手机');
        }
      });

      showToast('长按图片可保存到手机');
    }).catch(error => {
      console.error('加载Vant的showImagePreview失败:', error);
      showFailToast('预览截图失败');
    });
  } catch (error) {
    console.error('Vant图片预览失败:', error);
    showFailToast('预览截图失败');
  }
}

// 格式化数据为指定文本格式
const formatItemsToText = () => {
  if (!items.value || items.value.length === 0) {
    return '暂无数据';
  }

  // 将物品按类型分组
  const cigarettes = items.value.filter(item => item.stdType === '品规');

  if (cigarettes.length === 0) {
    return '暂无卷烟数据';
  }

  // 直接使用原始数据，不进行分组
  const sortedCigarettes = cigarettes
    .map(item => ({
      name: item.productName,
      quantity: Number(item.quantity),
      price: Number(item.price),
      priceUnit: item.priceUnit
    }))
    .sort((a, b) => b.quantity - a.quantity);

  // 格式化详细的卷烟数据（用于默认格式）
  const formattedCigarettes = sortedCigarettes.map(item => {
    return `${item.name}${item.quantity}条`;
  }).join('、');

  // 计算卷烟统计数据
  const cigarettesCount = sortedCigarettes.length; // 不同品规的数量

  // 使用更精确的方法计算总数量
  let cigarettesTotalQuantity = cigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
  cigarettesTotalQuantity = Math.round(cigarettesTotalQuantity * 10) / 10;

  // 计算总支数
  const totalSticks = Math.round(cigarettesTotalQuantity * 200);

  // 计算总金额
  const cigarettesTotalAmount = cigarettes.reduce((sum, item) => {
    return sum + Number(item.price) * Number(item.quantity);
  }, 0);

  const amountInWan = (cigarettesTotalAmount / 10000).toFixed(4);

  // 返回详细的格式化文本
  const totalSticksWan = (totalSticks / 10000).toFixed(2); // 支数保留两位小数
  return `卷烟：${formattedCigarettes}，卷烟合计共${cigarettesCount}个品种规格${cigarettesTotalQuantity}条(${totalSticksWan}万支)，总货值${amountInWan}万元。`;
}

// 模板相关数据
const templateList = ref([]);
const showTemplateDialog = ref(false);

// 获取模板字典数据
const getTemplateDictData = async () => {
  try {
    // 清空模板列表
    templateList.value = [];

    // 获取电子烟案件快报模板
    const electronicRes = await http.get('/api/dingapp/dict-biz/list?dictValue=电子烟案件快报')
    if (electronicRes.data && electronicRes.data.length > 0) {
      const electronicTemplates = await http.get('/api/dingapp/dict-biz/child-list?parentId=' + electronicRes.data[0].parentId)
      if (electronicTemplates.data && electronicTemplates.data.length > 0) {
        // 添加电子烟案件快报模板
        const templates = electronicTemplates.data.map(item => ({
          id: item.id,
          name: item.dictValue,
          template: item.dictKey
        }));
        templateList.value = [...templateList.value, ...templates];
      }
    }


  } catch (error) {
    console.error('获取模板数据失败:', error);
    showFailToast('获取模板数据失败: ' + (error.message || '未知错误'));
  }
}

// 显示模板选择弹窗
const copyFormattedText = () => {
  // 直接显示模板选择弹窗，即使没有其他模板，也至少有默认模板
  showTemplateDialog.value = true;

  // 如果还没有加载模板，则加载模板
  if (templateList.value.length === 0) {
    getTemplateDictData();
  }
}

// 复制默认格式的文本
const copyDefaultText = async () => {
  // 显示加载提示
  showToast({
    type: 'loading',
    message: '正在生成文本...',
    forbidClick: true,
    duration: 0
  });

  try {
    const text = formatItemsToText();

    // 尝试使用DingTalk的复制API（如果在钉钉环境中）
    if (typeof dd !== 'undefined' && dd.biz && dd.biz.util && dd.biz.util.copyToClipboard) {
      dd.biz.util.copyToClipboard({
        text: text,
        onSuccess: function() {
          closeToast();
          showSuccessToast('文本已复制到剪贴板');
        },
        onFail: function() {
          // 如果钉钉API失败，尝试其他方法
          fallbackCopy(text);
        }
      });
    } else {
      // 如果不在钉钉环境中，尝试使用标准方法
      await standardCopy(text);
    }
  } catch (error) {
    console.error('复制文本失败:', error);
    closeToast();
    showFailToast('复制文本失败: ' + (error.message || '未知错误'));
  }
}

// 生成简化的物品描述，用于模板
const generateSimplifiedItemDescription = () => {
  if (!items.value || items.value.length === 0) {
    return '暂无数据';
  }

  // 将物品按类型分组
  const cigarettes = items.value.filter(item => item.stdType === '品规');

  if (cigarettes.length === 0) {
    return '暂无卷烟数据';
  }

  // 直接使用原始数据，不进行分组
  const sortedCigarettes = cigarettes
    .map(item => ({
      name: item.productName,
      quantity: Number(item.quantity)
    }))
    .sort((a, b) => b.quantity - a.quantity);

  // 获取前5个品牌规格，用于简短描述
  const topBrands = sortedCigarettes.slice(0, 5).map(item => item.name);
  const hasMoreBrands = sortedCigarettes.length > 5;

  // 格式化卷烟品牌描述
  let brandsDescription = '';
  if (topBrands.length > 0) {
    brandsDescription = topBrands.join('、');
    if (hasMoreBrands) {
      brandsDescription += '等';
    }
  }

  // 计算卷烟统计数据
  const cigarettesCount = sortedCigarettes.length; // 不同品规的数量

  // 使用更精确的方法计算总数量
  let cigarettesTotalQuantity = cigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
  cigarettesTotalQuantity = Math.round(cigarettesTotalQuantity * 10) / 10;

  // 计算总支数
  const totalSticks = Math.round(cigarettesTotalQuantity * 200);

  // 计算总金额
  const cigarettesTotalAmount = cigarettes.reduce((sum, item) => {
    return sum + Number(item.price) * Number(item.quantity);
  }, 0);

  const amountInWan = (cigarettesTotalAmount / 10000).toFixed(4);

  // 为模板格式化的简化文本
  const totalSticksWan = (totalSticks / 10000).toFixed(2); // 支数保留两位小数
  return `涉及${brandsDescription}共${cigarettesCount}个品牌（规格）${totalSticksWan}万支，案值${amountInWan}万元。`;
}

// 根据模板替换内容并复制
const copyWithTemplate = async (template) => {
  // 显示加载提示
  showToast({
    type: 'loading',
    message: '正在生成文本...',
    forbidClick: true,
    duration: 0
  });

  try {
    // 获取简化的物品描述，用于模板
    const simplifiedItemDescription = generateSimplifiedItemDescription();

    // 准备替换数据
    const replacementData = {
      // 物品描述（简化版）
      '{itemDescription}': simplifiedItemDescription,
      // 案值（万元）
      '{caseValue}': (Number(totalAmount.value) / 10000).toFixed(4),
      // 品规数
      '{itemCount}': items.value.length.toString(),
      // 总条数
      '{totalQuantity}': totalQuantity.value.toString(),
      // 总金额
      '{totalAmount}': totalAmount.value,
      // 标题
      '{title}': title.value,
      // 案件相关信息
      '{enforcementAgency}': caseInfo.value.enforcementAgency,
      '{caseTime}': caseInfo.value.caseTime,
      '{address}': caseInfo.value.address,
      '{detailedAddress}': caseInfo.value.detailedAddress,
      '{jointEnforcementAgency}': caseInfo.value.jointEnforcementAgency,
      '{caseReason}': caseInfo.value.caseReason,
      '{partyInvolved}': caseInfo.value.partyInvolved,
      '{licenseNo}': caseInfo.value.licenseNo,

    };

    // 替换模板中的占位符
    let finalText = template;
    for (const [placeholder, value] of Object.entries(replacementData)) {
      finalText = finalText.replace(new RegExp(placeholder, 'g'), value || '');
    }

    // 尝试使用DingTalk的复制API（如果在钉钉环境中）
    if (typeof dd !== 'undefined' && dd.biz && dd.biz.util && dd.biz.util.copyToClipboard) {
      dd.biz.util.copyToClipboard({
        text: finalText,
        onSuccess: function() {
          closeToast();
          showSuccessToast('文本已复制到剪贴板');
        },
        onFail: function() {
          // 如果钉钉API失败，尝试其他方法
          fallbackCopy(finalText);
        }
      });
    } else {
      // 如果不在钉钉环境中，尝试使用标准方法
      await standardCopy(finalText);
    }
  } catch (error) {
    console.error('复制文本失败:', error);
    closeToast();
    showFailToast('复制文本失败: ' + (error.message || '未知错误'));
  }
}

// 标准复制方法
const standardCopy = async (text) => {
  try {
    // 使用Clipboard API复制文本
    await navigator.clipboard.writeText(text);
    closeToast();
    showSuccessToast('文本已复制到剪贴板');
  } catch (error) {
    console.error('Clipboard API失败:', error);
    // 如果Clipboard API失败，尝试使用传统方法
    fallbackCopy(text);
  }
}

// 后备复制方法
const fallbackCopy = (text) => {
  try {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    // 设置样式使其不可见但可选择
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    textarea.style.top = '0';
    textarea.style.left = '0';

    document.body.appendChild(textarea);

    // 在iOS上，需要特殊处理
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
      const range = document.createRange();
      range.selectNodeContents(textarea);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
      textarea.setSelectionRange(0, 999999);
    } else {
      textarea.select();
    }

    const successful = document.execCommand('copy');
    document.body.removeChild(textarea);

    if (successful) {
      closeToast();
      showSuccessToast('文本已复制到剪贴板');
    } else {
      throw new Error('execCommand返回false');
    }
  } catch (fallbackError) {
    console.error('备用复制方法也失败:', fallbackError);
    closeToast();
    showFailToast('复制文本失败，请手动复制');
  }
}




</script>

<style lang="scss" scoped>
.detail-table-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 0 0 50px;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    .back-button {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #323233;
    }

    .title {
      flex: 1;
      text-align: center;
      font-size: 18px;
      font-weight: 500;
      color: #323233;
      margin: 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .action-buttons {
      display: flex;
      align-items: center;

      .copy-button,
      .screenshot-button {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #323233;
        margin-left: 8px;
      }
    }
  }

  .section {
    margin: 12px 0;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.van-cell-group__title) {
    padding: 16px 16px 8px;
    font-size: 15px;
    font-weight: 500;
    color: #323233;
  }

  .table-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .table-header {
      display: flex;
      background-color: #f7f8fa;
      border-bottom: 1px solid #ebedf0;

      .col-item {
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        white-space: normal; /* 允许文本换行 */
        word-break: break-word; /* 在单词内部换行 */
        display: flex;
        align-items: center; /* 垂直居中 */
        min-height: 48px; /* 设置最小高度 */
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #ebedf0;
        align-items: stretch; /* 确保所有列的高度一致 */

        &:last-child {
          border-bottom: none;
        }

        .col-item {
          padding: 12px 16px;
          font-size: 14px;
          color: #646566;
          white-space: normal; /* 允许文本换行 */
          word-break: break-word; /* 在单词内部换行 */
          overflow: visible; /* 内容溢出时可见 */
          display: flex;
          align-items: center; /* 垂直居中 */
          min-height: 48px; /* 设置最小高度 */
        }
      }

      .table-row:nth-child(even) {
        background-color: #fafafa;
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  /* 模板弹窗样式 */
  :deep(.template-dialog) {
    .van-dialog__content {
      max-height: 60vh;
      overflow: hidden;
    }
  }

  .template-list-container {
    max-height: 50vh; /* 设置最大高度为视口高度的50%，留出标题和边距空间 */
    overflow-y: auto; /* 启用垂直滚动 */
    -webkit-overflow-scrolling: touch; /* 在iOS上启用平滑滚动 */

    /* 适配横屏模式 */
    @media screen and (orientation: landscape) {
      max-height: 70vh; /* 横屏时可以更高一些 */
    }
  }

  .template-list {
    padding: 16px 16px 60px;

    .template-button {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .default-template {
      background-color: #1989fa;
      font-weight: bold;
    }

    .template-divider {
      position: relative;
      margin: 16px 0;
      text-align: center;
      color: #969799;
      font-size: 14px;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30%;
        height: 1px;
        background-color: #ebedf0;
      }

      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }
  }
}
</style>
